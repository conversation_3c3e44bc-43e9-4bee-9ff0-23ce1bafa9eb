<template>
  <div class="language-switcher">
    <select 
      v-model="currentLanguage" 
      @change="changeLanguage"
      class="language-select"
    >
      <option value="en">English</option>
      <option value="zh">中文</option>
    </select>
  </div>
</template>

<script>
import { useI18n } from 'vue-i18n'
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { updateTitleByRoute } from '../utils/titleManager.js'

export default {
  name: 'LanguageSwitcher',
  setup() {
    const { locale } = useI18n()
    const route = useRoute()
    const currentLanguage = ref(locale.value)
    
    // Watch for locale changes and sync with select
    watch(locale, (newLocale) => {
      currentLanguage.value = newLocale
    })
    
    const changeLanguage = (event) => {
      const newLang = event.target.value
      locale.value = newLang
      localStorage.setItem('preferred-language', newLang)
      
      // 更新页面标题
      updateTitleByRoute(route.name)
      
      // Optional: Send preference to backend
      // await api.updateUserPreference({ language: newLang })
    }
    
    return {
      currentLanguage,
      changeLanguage
    }
  }
}
</script>

<style scoped>
.language-switcher {
  display: inline-flex;
  align-items: center;
}

.language-select {
  background: white;
  border: 1px solid var(--color-slate-300);
  border-radius: var(--radius-md);
  padding: var(--spacing-2) var(--spacing-3);
  font-size: 0.875rem;
  cursor: pointer;
  outline: none;
  transition: all 0.15s ease;
  color: var(--color-slate-700);
}

.language-select:hover {
  border-color: var(--color-slate-400);
}

.language-select:focus {
  border-color: var(--color-blue-500);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}
</style>