<template>
  <div class="admin-dashboard">
    <div class="dashboard-header">
      <div class="header-content">
        <h1>{{ $t('admin.dashboard.title') }}</h1>
        <p>{{ $t('admin.dashboard.subtitle') }}</p>
      </div>
      <div class="header-actions">
        <button @click="handleLogout" class="logout-btn">
          {{ $t('dashboard.logout') }}
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon total-users"></div>
        <div class="stat-content">
          <h3>{{ dashboardData?.users?.total || 0 }}</h3>
          <p>{{ $t('admin.dashboard.stats.totalUsers') }}</p>
          <span class="stat-detail">{{ $t('admin.dashboard.monthlyNew') }}: {{ dashboardData?.users?.active_monthly || 0 }}</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon total-lessons"></div>
        <div class="stat-content">
          <h3>{{ dashboardData?.content?.lessons || 0 }}</h3>
          <p>{{ $t('admin.dashboard.stats.totalLessons') }}</p>
          <span class="stat-detail">{{ dashboardData?.content?.modules || 0 }} {{ $t('admin.dashboard.modules') }}</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon total-exercises"></div>
        <div class="stat-content">
          <h3>{{ dashboardData?.content?.exercises || 0 }}</h3>
          <p>{{ $t('admin.dashboard.stats.totalExercises') }}</p>
          <span class="stat-detail">{{ $t('admin.dashboard.codeExercises') }}</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon success-rate"></div>
        <div class="stat-content">
          <h3>{{ dashboardData?.activity?.success_rate || 0 }}%</h3>
          <p>{{ $t('admin.dashboard.stats.successRate') }}</p>
          <span class="stat-detail">{{ dashboardData?.activity?.total_submissions || 0 }} {{ $t('admin.dashboard.submissions') }}</span>
        </div>
      </div>
    </div>

    <!-- 主要操作 -->
    <div class="main-actions">
      <div class="action-card">
        <h3>{{ $t('admin.dashboard.contentManagement') }}</h3>
        <p>{{ $t('admin.dashboard.contentManagementDesc') }}</p>
        <div class="action-buttons">
          <router-link to="/admin/modules" class="btn btn-primary">
            {{ $t('admin.modules.title') }}
          </router-link>
          <router-link to="/admin/lessons" class="btn btn-primary">
            {{ $t('admin.lessons.title') }}
          </router-link>
          <router-link to="/admin/exercises" class="btn btn-primary">
            {{ $t('admin.exercises.title') }}
          </router-link>
        </div>
      </div>

      <div class="action-card">
        <h3>{{ $t('admin.dashboard.userManagement') }}</h3>
        <p>{{ $t('admin.dashboard.userManagementDesc') }}</p>
        <div class="action-buttons">
          <router-link to="/admin/users" class="btn btn-primary">
            {{ $t('admin.users.title') }}
          </router-link>
          <router-link to="/admin/analytics" class="btn btn-primary">
            {{ $t('admin.dashboard.learningAnalytics') }}
          </router-link>
          <!-- NEW: 研究分析链接 -->
          <router-link to="/research" class="btn btn-primary">
            {{ $t('navigation.research') }}
          </router-link>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity">
      <h2>{{ $t('admin.dashboard.recentActivity') }}</h2>
      
      <div class="activity-grid">
        <!-- 新注册用户 -->
        <div class="activity-section">
          <h3>{{ $t('admin.dashboard.recentUsers') }}</h3>
          <div class="activity-list">
            <div 
              v-for="user in dashboardData?.users?.recent || []" 
              :key="user.user_id"
              class="activity-item"
            >
              <div class="activity-icon user-icon">
                <div class="activity-icon-inner user"></div>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ user.username }}</div>
                <div class="activity-time">{{ formatDate(user.created_at) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近提交 -->
        <div class="activity-section">
          <h3>{{ $t('admin.dashboard.recentSubmissions') }}</h3>
          <div class="activity-list">
            <div 
              v-for="submission in dashboardData?.activity?.recent_submissions || []" 
              :key="submission.submission_id"
              class="activity-item"
            >
              <div class="activity-icon" :class="submission.is_correct ? 'success' : 'error'">
                <div class="activity-icon-inner" :class="submission.is_correct ? 'success' : 'error'"></div>
              </div>
              <div class="activity-content">
                <div class="activity-title">
                  {{ $t('admin.dashboard.userExercise', { userId: submission.user_id, exerciseId: submission.exercise_id }) }}
                </div>
                <div class="activity-time">{{ formatDate(submission.timestamp) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { adminAPI } from '../adminApi.js'
import { authAPI } from '../api.js'

export default {
  name: 'AdminDashboard',
  setup() {
    const { t } = useI18n()
    const dashboardData = ref(null)
    const loading = ref(false)

    const loadDashboard = async () => {
      try {
        loading.value = true
        window.setLoading(true)
        
        const data = await adminAPI.getDashboard()
        dashboardData.value = data
      } catch (error) {
        console.error('加载仪表板失败:', error)
        window.showNotification(t('dashboard.loadDashboardError'), 'error')
      } finally {
        loading.value = false
        window.setLoading(false)
      }
    }

    const formatDate = (timestamp) => {
      return new Date(timestamp).toLocaleString('zh-CN')
    }

    const handleLogout = () => {
      if (confirm(t('dashboard.confirmLogout'))) {
        authAPI.logout()
      }
    }

    onMounted(() => {
      loadDashboard()
    })

    return {
      dashboardData,
      loading,
      loadDashboard,
      formatDate,
      handleLogout,
      t
    }
  }
}
</script>

<style scoped>
.admin-dashboard {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem 1rem 2rem 1rem;
  background-color: var(--color-slate-50);
  min-height: 100vh;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 管理员仪表板头部样式 - 现代化设计 */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--color-slate-200);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-blue-500) 0%, var(--color-purple-500) 50%, var(--color-green-500) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dashboard-header:hover::before {
  opacity: 0.8;
}

.dashboard-header:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.dashboard-header h1 {
  color: var(--color-slate-900);
  margin-bottom: 0.75rem;
  font-size: 2.25rem;
  font-weight: 700;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.dashboard-header p {
  color: var(--color-slate-600);
  font-size: 1.125rem;
  margin: 0;
  line-height: 1.6;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

/* 统计卡片样式 - 管理员专用设计 */
.stat-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--color-slate-200);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-blue-600), var(--color-red-600));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.stat-card:hover::before {
  opacity: 1;
}

/* 统计图标样式 - 现代化图标系统 */
.stat-icon {
  width: 4rem;
  height: 4rem;
  margin-right: 1.5rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
}

.stat-icon::before {
  content: '';
  width: 2rem;
  height: 2rem;
  border-radius: 4px;
}

/* 各种统计图标的样式 */
.stat-icon.total-users {
  background: linear-gradient(135deg, var(--color-blue-100), var(--color-blue-200));
}

.stat-icon.total-users::before {
  background: var(--color-blue-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.stat-icon.total-lessons {
  background: linear-gradient(135deg, var(--color-green-100), var(--color-green-200));
}

.stat-icon.total-lessons::before {
  background: var(--color-green-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.stat-icon.total-exercises {
  background: linear-gradient(135deg, var(--color-purple-100), var(--color-purple-200));
}

.stat-icon.total-exercises::before {
  background: var(--color-purple-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.stat-icon.success-rate {
  background: linear-gradient(135deg, var(--color-amber-100), var(--color-amber-200));
}

.stat-icon.success-rate::before {
  background: var(--color-amber-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.stat-card:hover .stat-icon {
  transform: scale(1.1) rotate(5deg);
}

/* 统计内容样式 */
.stat-content h3 {
  font-size: 2.5rem;
  margin: 0;
  color: var(--color-slate-900);
  font-weight: 700;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.stat-content p {
  margin: 0.5rem 0 0.25rem;
  color: var(--color-slate-600);
  font-weight: 600;
  font-size: 1rem;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.stat-detail {
  font-size: 0.875rem;
  color: var(--color-slate-500);
  font-weight: 500;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 主要操作区域 */
.main-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

/* 操作卡片样式 - 增强版 */
.action-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--color-slate-200);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-blue-600), var(--color-green-600));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.action-card:hover::before {
  opacity: 1;
}

.action-card h3 {
  color: var(--color-slate-900);
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 700;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.action-card p {
  color: var(--color-slate-600);
  margin-bottom: 1.5rem;
  line-height: 1.6;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.action-buttons {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

/* 通用按钮样式 */
.btn {
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  border: 1px solid transparent;
  font-size: 0.875rem;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: white;
  color: var(--color-blue-600);
  border-color: var(--color-blue-200);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.btn-primary:hover {
  background: var(--color-blue-50);
  border-color: var(--color-blue-300);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  color: var(--color-blue-700);
}

.btn-secondary {
  background: white;
  color: var(--color-slate-600);
  border-color: var(--color-slate-200);
  box-shadow: 0 2px 4px rgba(148, 163, 184, 0.1);
}

.btn-secondary:hover {
  background: var(--color-slate-50);
  border-color: var(--color-slate-300);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(148, 163, 184, 0.15);
  color: var(--color-slate-700);
}

/* 最近活动区域 */
.recent-activity {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--color-slate-200);
  transition: all 0.3s ease;
}

.recent-activity:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.recent-activity h2 {
  color: var(--color-slate-900);
  margin-bottom: 1.5rem;
  font-size: 1.75rem;
  font-weight: 700;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.activity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.activity-section h3 {
  color: var(--color-slate-700);
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* 活动项目样式 - 现代化设计 */
.activity-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, var(--color-slate-50) 0%, whitefff 100%);
  border-radius: 10px;
  border: 1px solid var(--color-slate-200);
  transition: all 0.3s ease;
}

.activity-item:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: var(--color-blue-600);
}

/* 活动图标样式 */
.activity-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1rem;
  background: var(--color-slate-200);
  transition: transform 0.2s ease;
}

.activity-item:hover .activity-icon {
  transform: scale(1.1);
}

.activity-icon.success {
  background: linear-gradient(135deg, var(--color-green-50), var(--color-green-100));
  color: var(--color-green-800);
}

.activity-icon.error {
  background: linear-gradient(135deg, var(--color-red-100), var(--color-red-200));
  color: var(--color-red-800);
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: var(--color-slate-900);
  margin-bottom: 0.25rem;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.activity-time {
  font-size: 0.875rem;
  color: var(--color-slate-500);
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 0.5rem;
  }
  
  .dashboard-header {
    padding: 1.5rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .dashboard-header h1 {
    font-size: 1.875rem;
  }
  
  .dashboard-header p {
    font-size: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .stat-card {
    padding: 1.5rem;
  }
  
  .stat-icon {
    font-size: 2.5rem;
    margin-right: 1rem;
  }
  
  .main-actions {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .action-card {
    padding: 1.5rem;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
  
  .activity-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .recent-activity {
    padding: 1.5rem;
  }
}

/* 用户列表中的用户图标 */
.user-icon {
  width: 2.5rem;
  height: 2.5rem;
  margin-right: 1rem;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--color-indigo-100), var(--color-indigo-200));
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
}

.user-icon::before {
  content: '';
  width: 1.2rem;
  height: 1.2rem;
  background: var(--color-indigo-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

/* 活动指示图标 */
.activity-icon-inner {
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 0.75rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.activity-icon-inner::before {
  content: '';
  width: 100%;
  height: 100%;
  background: var(--color-gray-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M13 10V3L4 14h7v7l9-11h-7z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

/* 成功状态图标 */
.activity-icon-inner.success::before {
  background: var(--color-green-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M5 13l4 4L19 7'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

/* 错误状态图标 */
.activity-icon-inner.error::before {
  background: var(--color-red-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 18L18 6M6 6l12 12'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

/* 用户活动图标 */
.activity-icon-inner.user::before {
  background: var(--color-blue-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

/* 添加成功样式按钮 */
.btn-success {
  border-color: var(--color-green-500);
  color: var(--color-green-600);
}

.btn-success:hover {
  background: var(--color-green-50);
  border-color: var(--color-green-600);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.25);
}

@media (min-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .main-actions {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>