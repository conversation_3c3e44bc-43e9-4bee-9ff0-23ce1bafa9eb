<template>
  <div class="auth-container">
    <div class="auth-card">
      <h1 class="auth-title">🚀 {{ $t('auth.registerTitle') }}</h1>
      
      <form @submit.prevent="handleRegister">
        <div class="form-group">
          <label class="form-label">{{ $t('auth.username') }}</label>
          <input
            v-model="registerForm.username"
            type="text"
            class="form-input"
            :placeholder="$t('auth.usernamePlaceholder')"
            required
            minlength="3"
          />
        </div>
        
        <div class="form-group">
          <label class="form-label">{{ $t('auth.email') }}</label>
          <input
            v-model="registerForm.email"
            type="email"
            class="form-input"
            :placeholder="$t('auth.emailPlaceholder')"
            required
          />
        </div>
        
        <div class="form-group">
          <label class="form-label">{{ $t('auth.password') }}</label>
          <input
            v-model="registerForm.password"
            type="password"
            class="form-input"
            :placeholder="$t('auth.passwordPlaceholder') + ' (6+ characters)'"
            required
            minlength="6"
          />
        </div>
        
        <div class="form-group">
          <label class="form-label">{{ $t('auth.confirmPassword') }}</label>
          <input
            v-model="confirmPassword"
            type="password"
            class="form-input"
            :placeholder="$t('auth.confirmPasswordPlaceholder')"
            required
          />
        </div>
        
        <button type="submit" class="btn btn-primary" :disabled="loading || !passwordsMatch">
          {{ loading ? $t('auth.registering') : $t('auth.registerButton') }}
        </button>
        
        <div v-if="!passwordsMatch && confirmPassword" class="error-message">
          {{ $t('auth.passwordMismatch') }}
        </div>
      </form>
      
      <div class="auth-link">
        {{ $t('auth.haveAccount') }}
        <router-link to="/login">{{ $t('auth.login') }}</router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { authAPI } from '../api.js'

export default {
  name: 'Register',
  setup() {
    const router = useRouter()
    const { t } = useI18n()
    const loading = ref(false)
    const registerForm = ref({
      username: '',
      email: '',
      password: ''
    })
    const confirmPassword = ref('')

    const passwordsMatch = computed(() => {
      return !confirmPassword.value || registerForm.value.password === confirmPassword.value
    })

    const handleRegister = async () => {
      if (!passwordsMatch.value) {
        window.showNotification(t('auth.passwordMismatch'), 'error')
        return
      }

      try {
        loading.value = true
        window.setLoading(true)
        
        await authAPI.register(registerForm.value)
        
        window.showNotification(t('auth.registerSuccess'), 'success')
        router.push('/dashboard')
      } catch (error) {
        console.error('Registration failed:', error)
        const message = error.response?.data?.error || t('auth.registerError')
        window.showNotification(message, 'error')
      } finally {
        loading.value = false
        window.setLoading(false)
      }
    }

    return {
      registerForm,
      confirmPassword,
      passwordsMatch,
      loading,
      handleRegister
    }
  }
}
</script>

<style scoped>
.error-message {
  color: var(--color-red-600);
  font-size: 14px;
  margin-top: 8px;
  text-align: center;
}
</style>