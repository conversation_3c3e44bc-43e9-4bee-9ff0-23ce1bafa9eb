<template>
  <div class="admin-exercises">
    <div class="page-header">
      <h1>{{ $t('admin.interface.exercises.title') }}</h1>
      <div class="header-actions">
        <select v-model="selectedLessonId" @change="loadExercises" class="lesson-select">
          <option value="">{{ $t('admin.interface.exercises.allLessons') }}</option>
          <option v-for="lesson in lessons" :key="lesson.lesson_id" :value="lesson.lesson_id">
            {{ lesson.title }}
          </option>
        </select>
        <button @click="showCreateModal = true" class="btn btn-primary">
          {{ $t('admin.interface.exercises.createNewExercise') }}
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <p>{{ $t('admin.interface.exercises.loadingExercises') }}</p>
    </div>

    <!-- 练习列表 -->
    <div v-if="exercises.length === 0 && !loading" class="empty-state">
      <p>{{ $t('admin.interface.exercises.noExercisesTitle') }}</p>
      <p>{{ $t('admin.interface.exercises.noExercisesDesc') }}</p>
    </div>
    
    <div v-else class="exercises-grid">
      <div 
        v-for="exercise in exercises" 
        :key="exercise.exercise_id"
        class="exercise-card"
      >
        <div class="exercise-header">
          <h3>{{ $t('admin.interface.exercises.exerciseNumber', { id: exercise.exercise_id }) }}</h3>
          <div class="exercise-actions">
            <button @click="editExercise(exercise)" class="btn btn-sm btn-secondary">
              {{ $t('admin.interface.exercises.edit') }}
            </button>
            <button @click="manageTestCases(exercise)" class="btn btn-sm btn-info">
              {{ $t('admin.interface.exercises.testCases') }}
            </button>
            <button @click="deleteExercise(exercise)" class="btn btn-sm btn-danger">
              {{ $t('admin.interface.exercises.delete') }}
            </button>
          </div>
        </div>
        
        <div class="exercise-meta">
          <span class="meta-item">课程: {{ getLessonTitle(exercise.lesson_id) }}</span>
          <span class="meta-item">测试用例: {{ exercise.test_cases?.length || 0 }} 个</span>
        </div>
        
        <div class="exercise-content">
          <div class="problem-statement">
            <h4>{{ $t('admin.interface.exercises.problemStatement') }}</h4>
            <p>{{ exercise.problem_statement }}</p>
          </div>
          
          <div v-if="exercise.hints" class="hints">
            <h4>{{ $t('admin.interface.exercises.hints') }}</h4>
            <ul>
              <li v-for="(hint, index) in parseHints(exercise.hints)" :key="index">
                {{ hint }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑练习对话框 -->
    <div v-if="showCreateModal || editingExercise" class="modal-overlay" @click="closeModal">
      <div class="modal-content large" @click.stop>
        <div class="modal-header">
          <h2>{{ editingExercise ? $t('admin.interface.exercises.editExercise') : $t('admin.interface.exercises.createNewExerciseTitle') }}</h2>
          <button @click="closeModal" class="close-btn">&times;</button>
        </div>
        
        <form @submit.prevent="saveExercise" class="modal-form">
          <div class="form-group">
            <label for="lesson_id">{{ $t('admin.interface.exercises.belongsToLesson') }}</label>
            <select
              id="lesson_id"
              v-model="exerciseForm.lesson_id"
              required
              class="form-input"
            >
              <option value="">{{ $t('admin.interface.exercises.pleaseSelectLesson') }}</option>
              <option v-for="lesson in lessons" :key="lesson.lesson_id" :value="lesson.lesson_id">
                {{ lesson.title }}
              </option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="problem_statement">{{ $t('admin.interface.exercises.problemStatement') }}</label>
            <textarea
              id="problem_statement"
              v-model="exerciseForm.problem_statement"
              :placeholder="$t('admin.interface.exercises.enterProblemStatement')"
              rows="6"
              required
              class="form-input"
            ></textarea>
          </div>
          
          <div class="form-group">
            <label for="hints">{{ $t('admin.interface.exercises.hintsPerLine') }}</label>
            <textarea
              id="hints"
              v-model="hintsText"
              :placeholder="$t('admin.interface.exercises.enterHints')"
              rows="4"
              class="form-input"
            ></textarea>
          </div>
          
          <div class="form-actions">
            <button type="button" @click="closeModal" class="btn btn-secondary">
              {{ $t('admin.interface.exercises.cancel') }}
            </button>
            <button type="submit" class="btn btn-primary" :disabled="saving">
              {{ saving ? $t('admin.interface.exercises.saving') : $t('admin.interface.exercises.save') }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 测试用例管理对话框 -->
    <div v-if="showTestCasesModal" class="modal-overlay" @click="closeTestCasesModal">
      <div class="modal-content large" @click.stop>
        <div class="modal-header">
          <h2>{{ $t('admin.interface.exercises.testCaseManagement.title') }}</h2>
          <button @click="closeTestCasesModal" class="close-btn">&times;</button>
        </div>
        
        <div class="modal-form">
          <div class="test-cases-header">
            <h3>{{ $t('admin.interface.exercises.testCaseManagement.exerciseTestCases', { id: currentExercise?.exercise_id }) }}</h3>
            <button @click="showAddTestCase = true" class="btn btn-primary">
              {{ $t('admin.interface.exercises.testCaseManagement.addTestCase') }}
            </button>
          </div>
          
          <div class="test-cases-list">
            <div 
              v-for="testCase in testCases" 
              :key="testCase.test_case_id"
              class="test-case-item"
            >
              <div class="test-case-header">
                <h4>{{ $t('admin.interface.exercises.testCaseManagement.testCaseNumber', { id: testCase.test_case_id }) }}</h4>
                <div class="test-case-actions">
                  <button @click="editTestCase(testCase)" class="btn btn-sm btn-secondary">
                    {{ $t('admin.interface.exercises.testCaseManagement.edit') }}
                  </button>
                  <button @click="deleteTestCase(testCase)" class="btn btn-sm btn-danger">
                    {{ $t('admin.interface.exercises.testCaseManagement.delete') }}
                  </button>
                </div>
              </div>
              
              <div class="test-case-content">
                <div class="test-case-field">
                  <label>{{ $t('admin.interface.exercises.testCaseManagement.inputData') }}</label>
                  <pre>{{ testCase.input_data || $t('admin.interface.exercises.testCaseManagement.noInput') }}</pre>
                </div>
                <div class="test-case-field">
                  <label>{{ $t('admin.interface.exercises.testCaseManagement.expectedOutput') }}</label>
                  <pre>{{ testCase.expected_output }}</pre>
                </div>
                <div class="test-case-field">
                  <label>{{ $t('admin.interface.exercises.testCaseManagement.visibility') }}</label>
                  <span class="visibility-badge" :class="testCase.is_hidden ? 'hidden' : 'visible'">
                    {{ testCase.is_hidden ? '🙈 ' + $t('admin.interface.exercises.testCaseManagement.hidden') : '👁️ ' + $t('admin.interface.exercises.testCaseManagement.visible') }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑测试用例对话框 -->
    <div v-if="showAddTestCase || editingTestCase" class="modal-overlay" @click="closeTestCaseModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2>{{ editingTestCase ? $t('admin.interface.exercises.testCaseManagement.editTestCase') : $t('admin.interface.exercises.testCaseManagement.addTestCaseTitle') }}</h2>
          <button @click="closeTestCaseModal" class="close-btn">&times;</button>
        </div>
        
        <form @submit.prevent="saveTestCase" class="modal-form">
          <div class="form-group">
            <label for="input_data">{{ $t('admin.interface.exercises.testCaseManagement.inputDataLabel') }}</label>
            <textarea
              id="input_data"
              v-model="testCaseForm.input_data"
              :placeholder="$t('admin.interface.exercises.testCaseManagement.inputDataPlaceholder')"
              rows="4"
              class="form-input"
            ></textarea>
          </div>
          
          <div class="form-group">
            <label for="expected_output">{{ $t('admin.interface.exercises.testCaseManagement.expectedOutputLabel') }}</label>
            <textarea
              id="expected_output"
              v-model="testCaseForm.expected_output"
              :placeholder="$t('admin.interface.exercises.testCaseManagement.expectedOutputPlaceholder')"
              rows="4"
              required
              class="form-input"
            ></textarea>
          </div>
          
          <div class="form-group">
            <div class="checkbox-group">
              <input
                id="is_hidden"
                v-model="testCaseForm.is_hidden"
                type="checkbox"
                class="form-checkbox"
              />
              <label for="is_hidden" class="checkbox-label">
                <span class="checkbox-icon">{{ testCaseForm.is_hidden ? '🙈' : '👁️' }}</span>
                {{ $t('admin.interface.exercises.testCaseManagement.hideFromUsers') }}
              </label>
            </div>
            <div class="form-help-text">
              {{ $t('admin.interface.exercises.testCaseManagement.hideFromUsersDesc') }}
            </div>
          </div>
          
          <div class="form-actions">
            <button type="button" @click="closeTestCaseModal" class="btn btn-secondary">
              {{ $t('admin.interface.exercises.testCaseManagement.cancel') }}
            </button>
            <button type="submit" class="btn btn-primary" :disabled="saving">
              {{ saving ? $t('admin.interface.exercises.testCaseManagement.saving') : $t('admin.interface.exercises.testCaseManagement.save') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { adminAPI } from '../adminApi.js'

export default {
  name: 'AdminExercises',
  setup() {
    const { t } = useI18n()
    const exercises = ref([])
    const lessons = ref([])
    const testCases = ref([])
    const selectedLessonId = ref('')
    const showCreateModal = ref(false)
    const showTestCasesModal = ref(false)
    const showAddTestCase = ref(false)
    const editingExercise = ref(null)
    const editingTestCase = ref(null)
    const currentExercise = ref(null)
    const saving = ref(false)
    const loading = ref(false)
    
    const exerciseForm = ref({
      lesson_id: '',
      problem_statement: '',
      hints: ''
    })

    const testCaseForm = ref({
      input_data: '',
      expected_output: '',
      is_hidden: false
    })

    const hintsText = ref('')

    const loadLessons = async () => {
      try {
        const data = await adminAPI.getLessons()
        lessons.value = data.lessons || []
      } catch (error) {
        console.error('加载课程失败:', error)
        window.showNotification(t('admin.interface.exercises.loadLessonsError'), 'error')
      }
    }

    const loadExercises = async () => {
      try {
        loading.value = true
        const data = await adminAPI.getExercises(selectedLessonId.value || null)
        exercises.value = data.exercises || []
      } catch (error) {
        console.error('加载练习失败:', error)
        window.showNotification(t('admin.interface.exercises.loadExercisesError'), 'error')
      } finally {
        loading.value = false
      }
    }

    const loadTestCases = async (exerciseId) => {
      try {
        const data = await adminAPI.getTestCases(exerciseId)
        testCases.value = data.test_cases || []
      } catch (error) {
        console.error('加载测试用例失败:', error)
        window.showNotification(t('admin.interface.exercises.testCaseManagement.loadTestCasesError'), 'error')
      }
    }

    const getLessonTitle = (lessonId) => {
      const lesson = lessons.value.find(l => l.lesson_id === lessonId)
      return lesson ? lesson.title : t('admin.interface.exercises.unknownLesson')
    }

    const parseHints = (hints) => {
      if (!hints) return []
      
      // 如果已经是数组，直接返回
      if (Array.isArray(hints)) {
        return hints
      }
      
      // 如果是字符串，尝试解析
      if (typeof hints === 'string') {
        try {
          const parsed = JSON.parse(hints)
          return Array.isArray(parsed) ? parsed : [hints]
        } catch {
          return hints.split('\n').filter(h => h.trim())
        }
      }
      
      // 其他情况返回空数组
      return []
    }

    const editExercise = (exercise) => {
      editingExercise.value = exercise
      exerciseForm.value = {
        lesson_id: exercise.lesson_id,
        problem_statement: exercise.problem_statement,
        hints: exercise.hints || ''
      }
      
      // 解析提示为文本
      const hints = parseHints(exercise.hints)
      hintsText.value = Array.isArray(hints) ? hints.join('\n') : ''
    }

    const manageTestCases = (exercise) => {
      currentExercise.value = exercise
      showTestCasesModal.value = true
      loadTestCases(exercise.exercise_id)
    }

    const editTestCase = (testCase) => {
      editingTestCase.value = testCase
      testCaseForm.value = {
        input_data: testCase.input_data || '',
        expected_output: testCase.expected_output || '',
        is_hidden: testCase.is_hidden || false
      }
      showAddTestCase.value = true
    }

    const closeModal = () => {
      showCreateModal.value = false
      editingExercise.value = null
      exerciseForm.value = {
        lesson_id: selectedLessonId.value || '',
        problem_statement: '',
        hints: ''
      }
      hintsText.value = ''
    }

    const closeTestCasesModal = () => {
      showTestCasesModal.value = false
      currentExercise.value = null
      testCases.value = []
    }

    const closeTestCaseModal = () => {
      showAddTestCase.value = false
      editingTestCase.value = null
      testCaseForm.value = {
        input_data: '',
        expected_output: '',
        is_hidden: false
      }
    }

    const saveExercise = async () => {
      try {
        saving.value = true
        
        // 处理提示
        const hints = hintsText.value.trim() 
          ? JSON.stringify(hintsText.value.split('\n').filter(h => h.trim()))
          : ''
        
        const formData = {
          ...exerciseForm.value,
          hints
        }
        
        if (editingExercise.value) {
          await adminAPI.updateExercise(editingExercise.value.exercise_id, formData)
          window.showNotification(t('admin.interface.exercises.exerciseUpdateSuccess'), 'success')
        } else {
          await adminAPI.createExercise(formData)
          window.showNotification(t('admin.interface.exercises.exerciseCreateSuccess'), 'success')
        }
        
        closeModal()
        await loadExercises()
      } catch (error) {
        console.error('保存练习失败:', error)
        const message = error.response?.data?.error || t('admin.interface.exercises.saveFailed')
        window.showNotification(message, 'error')
      } finally {
        saving.value = false
      }
    }

    const saveTestCase = async () => {
      try {
        saving.value = true
        
        if (editingTestCase.value) {
          await adminAPI.updateTestCase(
            currentExercise.value.exercise_id,
            editingTestCase.value.test_case_id,
            testCaseForm.value
          )
          window.showNotification(t('admin.interface.exercises.testCaseManagement.testCaseUpdateSuccess'), 'success')
        } else {
          await adminAPI.createTestCase(
            currentExercise.value.exercise_id,
            testCaseForm.value
          )
          window.showNotification(t('admin.interface.exercises.testCaseManagement.testCaseCreateSuccess'), 'success')
        }
        
        closeTestCaseModal()
        await loadTestCases(currentExercise.value.exercise_id)
      } catch (error) {
        console.error('保存测试用例失败:', error)
        const message = error.response?.data?.error || t('admin.interface.exercises.saveFailed')
        window.showNotification(message, 'error')
      } finally {
        saving.value = false
      }
    }

    const deleteExercise = async (exercise) => {
      if (!confirm(t('admin.interface.exercises.exerciseDeleteConfirm', { id: exercise.exercise_id }))) {
        return
      }
      
      try {
        await adminAPI.deleteExercise(exercise.exercise_id)
        window.showNotification(t('admin.interface.exercises.exerciseDeleteSuccess'), 'success')
        await loadExercises()
      } catch (error) {
        console.error('删除练习失败:', error)
        const message = error.response?.data?.error || t('admin.interface.exercises.deleteFailed')
        window.showNotification(message, 'error')
      }
    }

    const deleteTestCase = async (testCase) => {
      if (!confirm(t('admin.interface.exercises.testCaseManagement.testCaseDeleteConfirm', { id: testCase.test_case_id }))) {
        return
      }
      
      try {
        await adminAPI.deleteTestCase(
          currentExercise.value.exercise_id,
          testCase.test_case_id
        )
        window.showNotification(t('admin.interface.exercises.testCaseManagement.testCaseDeleteSuccess'), 'success')
        await loadTestCases(currentExercise.value.exercise_id)
      } catch (error) {
        console.error('删除测试用例失败:', error)
        const message = error.response?.data?.error || t('admin.interface.exercises.deleteFailed')
        window.showNotification(message, 'error')
      }
    }

    onMounted(() => {
      loadLessons()
      loadExercises()
    })

    return {
      exercises,
      lessons,
      testCases,
      selectedLessonId,
      showCreateModal,
      showTestCasesModal,
      showAddTestCase,
      editingExercise,
      editingTestCase,
      currentExercise,
      saving,
      loading,
      exerciseForm,
      testCaseForm,
      hintsText,
      loadExercises,
      getLessonTitle,
      parseHints,
      editExercise,
      manageTestCases,
      editTestCase,
      closeModal,
      closeTestCasesModal,
      closeTestCaseModal,
      saveExercise,
      saveTestCase,
      deleteExercise,
      deleteTestCase
    }
  }
}
</script>

<style scoped>
.admin-exercises {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: var(--color-slate-800);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.lesson-select {
  padding: 8px 12px;
  border: 2px solid var(--color-slate-200);
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.exercises-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
  gap: 20px;
}

.exercise-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.exercise-card:hover {
  transform: translateY(-5px);
}

.exercise-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.exercise-header h3 {
  color: var(--color-slate-800);
  margin: 0;
  font-size: 1.2em;
}

.exercise-actions {
  display: flex;
  gap: 8px;
}

.exercise-meta {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.meta-item {
  font-size: 14px;
  color: var(--color-slate-500);
}

.exercise-content {
  margin-bottom: 15px;
}

.problem-statement {
  background: var(--color-slate-50);
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
}

.problem-statement h4 {
  color: var(--color-slate-800);
  margin: 0 0 10px 0;
  font-size: 16px;
}

.problem-statement p {
  margin: 0;
  color: var(--color-slate-600);
  line-height: 1.5;
}

.hints {
  background: #e8f5e8;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid var(--color-green-600);
}

.hints h4 {
  color: var(--color-green-800);
  margin: 0 0 10px 0;
  font-size: 16px;
}

.hints ul {
  margin: 0;
  padding-left: 20px;
}

.hints li {
  color: var(--color-green-800);
  margin-bottom: 5px;
}

.test-cases-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.test-cases-header h3 {
  color: var(--color-slate-800);
  margin: 0;
}

.test-cases-list {
  max-height: 400px;
  overflow-y: auto;
}

.test-case-item {
  background: var(--color-slate-50);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  border: 1px solid var(--color-slate-200);
}

.test-case-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.test-case-header h4 {
  color: var(--color-slate-800);
  margin: 0;
  font-size: 16px;
}

.test-case-actions {
  display: flex;
  gap: 8px;
}

.test-case-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.test-case-field label {
  display: block;
  font-weight: 500;
  color: var(--color-slate-800);
  margin-bottom: 5px;
}

.test-case-field pre {
  background: white;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid var(--color-slate-300);
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  margin: 0;
  overflow-x: auto;
}

.btn {
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  border: 1px solid transparent;
  font-size: 0.875rem;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: white;
  color: var(--color-blue-600);
  border-color: var(--color-blue-200);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-blue-50);
  border-color: var(--color-blue-300);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  color: var(--color-blue-700);
}

.btn-secondary {
  background: white;
  color: var(--color-slate-600);
  border-color: var(--color-slate-200);
  box-shadow: 0 2px 4px rgba(148, 163, 184, 0.1);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-slate-50);
  border-color: var(--color-slate-300);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(148, 163, 184, 0.15);
  color: var(--color-slate-700);
}

.btn-danger {
  background: white;
  color: var(--color-red-600);
  border-color: var(--color-red-200);
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.1);
}

.btn-danger:hover:not(:disabled) {
  background: var(--color-red-50);
  border-color: var(--color-red-300);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
  color: var(--color-red-700);
}

.btn-info {
  background: white;
  color: var(--color-blue-600);
  border-color: var(--color-blue-200);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.btn-info:hover:not(:disabled) {
  background: var(--color-blue-50);
  border-color: var(--color-blue-300);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  color: var(--color-blue-700);
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.8125rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-content.large {
  max-width: 900px;
  width: 95%;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--color-slate-100);
}

.modal-header h2 {
  margin: 0;
  color: var(--color-slate-800);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--color-slate-500);
}

.close-btn:hover {
  color: var(--color-slate-800);
}

.modal-form {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: var(--color-slate-800);
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 2px solid var(--color-slate-200);
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--color-blue-600);
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.loading-state {
  background: var(--color-slate-50);
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid var(--color-blue-600);
  text-align: center;
}

.loading-state p {
  margin: 0;
  color: var(--color-slate-600);
  font-size: 16px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.empty-state p {
  margin: 10px 0;
  color: var(--color-slate-500);
  font-size: 16px;
}

.empty-state p:first-child {
  font-size: 20px;
  font-weight: 500;
  color: var(--color-slate-600);
}

/* 测试用例可见性相关样式 */
.visibility-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
}

.visibility-badge.visible {
  background: var(--color-green-50);
  color: var(--color-green-800);
  border: 1px solid #c3e6cb;
}

.visibility-badge.hidden {
  background: var(--color-red-50);
  color: var(--color-red-800);
  border: 1px solid #f5c6cb;
}

/* 复选框样式 */
.checkbox-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.form-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--color-blue-600);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: 500;
  color: var(--color-slate-800);
  margin: 0;
}

.checkbox-icon {
  font-size: 16px;
  transition: all 0.2s ease;
}

.form-help-text {
  font-size: 14px;
  color: var(--color-slate-500);
  margin-top: 6px;
  font-style: italic;
}

/* 测试用例内容布局调整 */
.test-case-content {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 15px;
  align-items: start;
}

.test-case-field:last-child {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-width: 120px;
}
</style>