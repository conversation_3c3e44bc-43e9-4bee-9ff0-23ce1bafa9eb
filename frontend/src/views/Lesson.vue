<template>
  <div class="lesson-container" v-if="lesson">
    <!-- Enhanced Lesson Navigation with Progress -->
    <div class="lesson-nav modern-card">
      <div class="nav-content">
        <div class="nav-left">
          <button @click="$router.go(-1)" class="btn btn-secondary btn-with-icon">
            <svg class="icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
            </svg>
            {{ $t('lesson.backToCourseList') }}
          </button>
        </div>
        <div class="lesson-title">
          <h1 class="lesson-title-text">{{ lesson.title }}</h1>
          <div class="lesson-progress">
            <div class="progress-badge-container">
              <span class="progress-badge modern-badge" :class="lesson.progress?.status">
                <span class="badge-indicator"></span>
                {{ getStatusText(lesson.progress?.status) }}
              </span>
            </div>
            <div class="lesson-meta">
              <span v-if="lesson.progress?.attempts" class="meta-item">
                <svg class="meta-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {{ $t('lesson.attemptCount', { count: lesson.progress.attempts }) }}
              </span>
              <span v-if="lesson.progress?.avg_time_sec && lesson.progress.avg_time_sec > 0" class="meta-item">
                <svg class="meta-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                </svg>
                {{ $t('lesson.avgSolvingTime') }}: {{ formatTimerTime(Math.round(lesson.progress.avg_time_sec)) }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <!-- Progress Bar -->
      <div class="lesson-progress-bar" v-if="lesson.exercises?.length">
        <div class="progress-track">
          <div class="progress-fill" :style="{ width: `${exerciseCompletionPercentage}%` }"></div>
        </div>
        <span class="progress-text">{{ completedExercises }}/{{ lesson.exercises.length }} {{ $t('lesson.exercisesCompleted') }}</span>
      </div>
    </div>

    <!-- Lesson Content Area -->
    <div class="lesson-content">
      <!-- Theoretical Content -->
      <div class="content-section">
        <div class="markdown-content" v-html="renderedContent"></div>
      </div>

      <!-- Enhanced Programming Exercises -->
      <div v-if="lesson.exercises?.length" class="exercises-section">
        <div class="section-header">
          <h2 class="section-title">{{ $t('lesson.programmingExercises') }}</h2>
          <div class="section-meta">
            <span class="exercise-count">{{ lesson.exercises.length }} {{ $t('lesson.exercises') }}</span>
          </div>
        </div>

        <div class="exercises-grid">
          <div
            v-for="(exercise, index) in lesson.exercises"
            :key="exercise.exercise_id"
            class="exercise-card modern-card"
            :class="{ 'completed': results[exercise.exercise_id]?.is_correct }"
          >
            <div class="exercise-header">
              <div class="exercise-title-section">
                <div class="exercise-number">
                  <span class="number">{{ index + 1 }}</span>
                  <div class="completion-indicator" v-if="results[exercise.exercise_id]?.is_correct">
                    <svg class="check-icon" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </div>
                </div>
                <h3 class="exercise-title">{{ $t('lesson.exerciseNumber', { number: index + 1 }) }}</h3>
              </div>
              <div class="exercise-actions">
                <button
                  v-if="canShowHints(exercise)"
                  @click="requestHint(exercise.exercise_id)"
                  class="btn btn-warning btn-sm btn-with-icon"
                  :disabled="hintLoading"
                >
                  <svg class="icon" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                  </svg>
                  {{ $t('lesson.getHint') }}
                </button>
                <button
                  @click="toggleHistory(exercise.exercise_id)"
                  class="btn btn-secondary btn-sm btn-with-icon"
                  :disabled="historyLoading"
                >
                  <svg class="icon" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                  </svg>
                  {{ $t('lesson.viewHistory') }}
                </button>
              </div>
            </div>

          <div class="exercise-content">
            <div class="problem-statement">
              <p>{{ exercise.problem_statement }}</p>
            </div>

            <!-- Hints Display -->
            <div v-if="hints[exercise.exercise_id]" class="hints-section">
              <h4>{{ $t('lesson.hints') }}</h4>
              <div 
                v-for="(hint, hintIndex) in hints[exercise.exercise_id]" 
                :key="hintIndex"
                class="hint-item"
              >
                <strong>{{ $t('lesson.hintNumber', { number: hintIndex + 1 }) }}</strong> {{ hint }}
              </div>
            </div>

            <!-- Submission History Display -->
            <div v-if="histories[exercise.exercise_id]" class="history-section">
              <h4>{{ $t('lesson.submissionHistory') }}</h4>
              <div class="history-list">
                <div 
                  v-for="(submission, historyIndex) in histories[exercise.exercise_id]" 
                  :key="submission.submission_id"
                  class="history-item clickable"
                  :class="{ 'success': submission.is_correct }"
                  @click="showSubmissionCode(submission)"
                  :title="$t('lesson.clickToViewCode')"
                >
                  <span class="history-time">{{ formatTime(submission.timestamp) }}</span>
                  <span class="history-status">{{ submission.is_correct ? '✅' : '❌' }}</span>
                  <span class="history-attempts">{{ $t('lesson.attempt', { number: histories[exercise.exercise_id].length - historyIndex }) }}</span>
                  <span v-if="submission.error_type && !submission.is_correct" class="history-error">
                    ({{ $t(`lesson.errorTypes.${submission.error_type}`) || submission.error_type }})
                  </span>
                  <span class="history-hint">👁️</span>
                </div>
              </div>
            </div>

            <!-- Enhanced Code Editor -->
            <div class="code-editor-section modern-card">
              <div class="editor-header">
                <div class="editor-title-section">
                  <div class="editor-title">
                    <svg class="editor-icon" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                    <span>{{ $t('lesson.pythonCodeEditor') }}</span>
                  </div>
                  <!-- Enhanced Timer Display -->
                  <div v-if="hasStartedCoding[exercise.exercise_id]" class="coding-timer">
                    <div class="timer-indicator">
                      <svg class="timer-icon" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                      </svg>
                      <span class="timer-text">{{ formatTimerTime(currentTimes[exercise.exercise_id] || 0) }}</span>
                    </div>
                  </div>
                </div>
                <div class="editor-controls">
                  <button
                    @click="toggleTheme"
                    class="btn btn-ghost btn-sm btn-with-icon"
                    :title="$t('lesson.toggleTheme')"
                  >
                    <svg v-if="isDarkTheme" class="icon" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd" />
                    </svg>
                    <svg v-else class="icon" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                    </svg>
                  </button>
                  <button
                    @click="runCode(exercise)"
                    class="btn btn-success btn-with-icon"
                    :disabled="codeRunning"
                    :class="{ 'btn-loading': codeRunning }"
                  >
                    <svg v-if="!codeRunning" class="icon" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                    </svg>
                    <div v-else class="loading-spinner"></div>
                    {{ codeRunning ? $t('lesson.running') : $t('lesson.runCode') }}
                  </button>
                </div>
              </div>
              <div class="code-editor-wrapper">
                <CodeEditor
                  v-model="codes[exercise.exercise_id]"
                  :theme="isDarkTheme ? 'dark' : 'light'"
                  height="280px"
                  :placeholder="$t('lesson.placeholder')"
                  @update:modelValue="onCodeChange(exercise.exercise_id, $event)"
                />
              </div>
            </div>

            <!-- Execution Results -->
            <div v-if="results[exercise.exercise_id]" class="result-section">
              <div class="result-header" :class="results[exercise.exercise_id].is_correct ? 'success' : 'error'">
                <span class="result-icon">
                  {{ results[exercise.exercise_id].is_correct ? '✅' : '❌' }}
                </span>
                <span class="result-text">
                  {{ results[exercise.exercise_id].is_correct ? $t('lesson.codeCorrect') : $t('lesson.codeNeedsImprovement') }}
                </span>
                <!-- 🔥 NEW: Feedback Comparison Toggle Button -->
                <div class="feedback-controls" v-if="results[exercise.exercise_id].comprehensive_feedback?.comprehensive_feedback || results[exercise.exercise_id].comprehensive_feedback?.ai_generated">
                  <button 
                    @click="toggleFeedbackMode(exercise.exercise_id)"
                    class="btn btn-sm btn-secondary"
                    :title="feedbackModes[exercise.exercise_id] === 'simple' ? $t('lesson.viewDetailedComparison') : $t('lesson.simpleView')"
                  >
                    {{ feedbackModes[exercise.exercise_id] === 'simple' ? $t('lesson.viewComparison') : $t('lesson.simpleMode') }}
                  </button>
                </div>
              </div>
              
              <!-- Simple Feedback Mode -->
              <div v-if="feedbackModes[exercise.exercise_id] === 'simple'" class="feedback-message">
                {{ results[exercise.exercise_id].feedback }}
              </div>

              <!-- 🔥 NEW: Detailed Feedback Comparison Mode -->
              <div v-else-if="results[exercise.exercise_id].comprehensive_feedback?.comprehensive_feedback || results[exercise.exercise_id].comprehensive_feedback?.ai_generated" class="comprehensive-feedback-section">
                <div class="feedback-tabs">
                  <button 
                    v-for="(feedbackData, feedbackType) in getAvailableFeedbacks(exercise.exercise_id)"
                    :key="feedbackType"
                    @click="!feedbackData.placeholder && (activeFeedbackTabs[exercise.exercise_id] = feedbackType)"
                    class="feedback-tab"
                    :class="{ 
                      active: activeFeedbackTabs[exercise.exercise_id] === feedbackType,
                      disabled: feedbackData.placeholder
                    }"
                    :disabled="feedbackData.placeholder"
                  >
                    <span v-if="feedbackData.loading" class="spinner-sm"></span>
                    {{ getFeedbackTypeLabel(feedbackType) }}
                    <span class="generation-time" v-if="feedbackData.generation_time">
                      ({{ Math.round(feedbackData.generation_time) }}ms)
                    </span>
                  </button>
                </div>
                
                <div class="feedback-content-area">
                  <div class="active-feedback-content">
                    <FeedbackComponents
                      :feedback-data="getActiveFeedbackData(exercise.exercise_id)"
                      :submission-id="results[exercise.exercise_id].submission?.submission_id"
                      :current-component="getFeedbackComponent(activeFeedbackTabs[exercise.exercise_id])"
                      @rate-feedback="handleFeedbackRating"
                    />
                  </div>
                  
                  <!-- Feedback Comparison Insights -->
                  <div class="feedback-insights" v-if="(results[exercise.exercise_id].comprehensive_feedback?.comprehensive_feedback || results[exercise.exercise_id].comprehensive_feedback)?.comparison">
                    <h5>{{ $t('lesson.feedbackComparison') }}</h5>
                    <div class="insights-content">
                      <p><strong>{{ $t('lesson.feedbackSources') }}</strong> {{ feedbackSourceInfo(exercise.exercise_id).count }} <span class="source-names">{{ feedbackSourceInfo(exercise.exercise_id).names }}</span></p>
                      <p><strong>{{ $t('lesson.recommendedPrimaryFeedback') }}</strong> {{ getFeedbackTypeLabel((results[exercise.exercise_id].comprehensive_feedback?.comprehensive_feedback || results[exercise.exercise_id].comprehensive_feedback)?.recommended_primary) }}</p>
                      <p><strong>{{ $t('lesson.learningStage') }}</strong> {{ getStageLabel((results[exercise.exercise_id].comprehensive_feedback?.comprehensive_feedback || results[exercise.exercise_id].comprehensive_feedback)?.learning_stage_adapted?.stage) }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Error Information -->
              <div v-if="!results[exercise.exercise_id].is_correct" class="error-details">
                <div v-if="results[exercise.exercise_id].error_message" class="error-message">
                  <strong>{{ $t('lesson.errorMessage') }}</strong>
                  <pre>{{ results[exercise.exercise_id].error_message }}</pre>
                </div>
                
                <div v-if="results[exercise.exercise_id].output" class="output-section">
                  <strong>{{ $t('lesson.yourOutput') }}:</strong>
                  <pre>{{ results[exercise.exercise_id].output }}</pre>
                </div>
                
                <div v-if="results[exercise.exercise_id].expected_output" class="expected-output">
                  <strong>{{ $t('lesson.expectedOutput') }}:</strong>
                  <pre>{{ results[exercise.exercise_id].expected_output }}</pre>
                </div>
              </div>

              <!-- Code Quality Analysis -->
              <div v-if="results[exercise.exercise_id].submission" class="code-quality-section">
                <h4>{{ $t('lesson.codeQualityAnalysis') }}</h4>
                <div class="quality-metrics">
                  <div class="metric" v-if="results[exercise.exercise_id].submission.code_lines">
                    <span class="metric-label">{{ $t('lesson.linesOfCode') }}</span>
                    <span class="metric-value">{{ results[exercise.exercise_id].submission.code_lines }}</span>
                  </div>
                  <div class="metric" v-if="results[exercise.exercise_id].submission.code_complexity">
                    <span class="metric-label">{{ $t('lesson.complexity') }}:</span>
                    <span class="metric-value">{{ results[exercise.exercise_id].submission.code_complexity.toFixed(1) }}</span>
                  </div>
                  <div class="metric" v-if="results[exercise.exercise_id].submission.syntax_score">
                    <span class="metric-label">{{ $t('lesson.qualityScore') }}</span>
                    <span class="metric-value quality-score" :class="getScoreClass(results[exercise.exercise_id].submission.syntax_score)">
                      {{ results[exercise.exercise_id].submission.syntax_score.toFixed(1) }}/10
                    </span>
                  </div>
                </div>
              </div>

              <!-- 🔧 修复：独立的改进建议板块 -->
              <div v-if="results[exercise.exercise_id].submission?.api_analysis_result" class="improvement-suggestions-section">
                <h4>{{ $t('lesson.improvementSuggestions') }}</h4>
                <div class="suggestions-list">
                  <div v-for="suggestion in getAnalysisSuggestions(results[exercise.exercise_id].submission.api_analysis_result)" 
                       :key="suggestion"
                       class="suggestion-item">
                    <span class="suggestion-text">{{ suggestion }}</span>
                  </div>
                </div>
                <div v-if="getAnalysisSuggestions(results[exercise.exercise_id].submission.api_analysis_result).length === 0" class="no-suggestions">
                  <span class="suggestion-icon">ℹ️</span>
                  <span class="suggestion-text">{{ $t('lesson.noSuggestionsAvailable') }}</span>
                </div>
              </div>

              <!-- Hint Availability Reminder -->
              <div v-if="!results[exercise.exercise_id].is_correct && results[exercise.exercise_id].hints_available" class="hint-reminder">
                {{ $t('lesson.hintReminder') }}
              </div>
            </div>

            <!-- Test Cases Display -->
            <div v-if="getVisibleTestCases(exercise).length" class="test-cases-section">
              <h4>{{ $t('lesson.testCases') }}</h4>
              <div 
                v-for="(testCase, testIndex) in getVisibleTestCases(exercise)" 
                :key="testIndex"
                class="test-case"
              >
                <div><strong>{{ $t('lesson.input') }}</strong> {{ testCase.input_data || $t('lesson.noInput') }}</div>
                <div><strong>{{ $t('lesson.expectedOutput') }}:</strong> {{ testCase.expected_output }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Course Completion Notification -->
    <div v-if="allExercisesCompleted" class="completion-banner">
      <div class="completion-content">
        <h3>{{ $t('lesson.congratulationsCompleted') }}</h3>
        <p>{{ $t('lesson.masteredAllConcepts') }}</p>
        <router-link to="/modules" class="btn btn-primary">
          {{ $t('lesson.continueToNextCourse') }}
        </router-link>
      </div>
    </div>
  </div>

  <div v-else-if="loading" class="loading-container">
    <div class="spinner"></div>
    <p>{{ $t('lesson.loadingContent') }}</p>
  </div>

  <div v-else class="error-container">
    <h3>{{ $t('lesson.loadFailed') }}</h3>
    <p>{{ $t('lesson.loadFailedMessage') }}</p>
    <button @click="loadLesson" class="btn btn-primary">{{ $t('lesson.reload') }}</button>
  </div>

  <!-- Submission Code Modal -->
  <div v-if="showCodeModal" class="code-modal-overlay" @click="closeCodeModal">
    <div class="code-modal" @click.stop>
      <div class="code-modal-header">
        <h3>{{ $t('lesson.submissionCodeTitle') }}</h3>
        <button class="close-btn" @click="closeCodeModal">×</button>
      </div>
      <div class="code-modal-info">
        <span class="modal-time">{{ formatTime(selectedSubmission?.timestamp) }}</span>
        <span class="modal-status">{{ selectedSubmission?.is_correct ? '✅ ' + $t('lesson.success') : '❌ ' + $t('lesson.failed') }}</span>
        <span v-if="selectedSubmission?.error_type && !selectedSubmission?.is_correct" class="modal-error">
          ({{ $t(`lesson.errorTypes.${selectedSubmission.error_type}`) || selectedSubmission.error_type }})
        </span>
      </div>
      <div class="code-modal-content">
        <pre><code>{{ selectedSubmission?.code || $t('lesson.noCodeAvailable') }}</code></pre>
      </div>
      <div class="code-modal-footer">
        <button class="btn btn-secondary" @click="closeCodeModal">{{ $t('common.close') }}</button>
        <button v-if="selectedSubmission?.code" class="btn btn-primary" @click="copyToCurrentEditor">
          {{ $t('lesson.copyToEditor') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { contentAPI, submissionAPI } from '../api.js'
import { marked } from 'marked'
import CodeEditor from '../components/CodeEditor.vue'
import FeedbackComponents from '../components/FeedbackComponents.vue'

export default {
  name: 'Lesson',
  components: {
    CodeEditor,
    FeedbackComponents
  },
  setup() {
    const route = useRoute()
    const { t } = useI18n()
    const lesson = ref(null)
    const loading = ref(false)
    const codeRunning = ref(false)
    const hintLoading = ref(false)
    const isDarkTheme = ref(false)
    
    // Store code, results and hints for each exercise
    const codes = ref({})
    const results = ref({})
    const hints = ref({})
    
    // Submission history for each exercise
    const histories = ref({})
    const historyLoading = ref(false)
    
    // Code modal for viewing submission code
    const showCodeModal = ref(false)
    const selectedSubmission = ref(null)
    
    // 🔥 NEW: Feedback mode and tab management
    const feedbackModes = ref({}) // 'simple' or 'detailed'
    const activeFeedbackTabs = ref({}) // Currently active feedback tab
    
    // 🔥 NEW: Time tracking for problem-solving duration
    const startTimes = ref({}) // Track when user starts working on each exercise
    const hasStartedCoding = ref({}) // Track if user has started typing code
    const currentTimes = ref({}) // Current elapsed time for each exercise (in seconds)
    const timers = ref({}) // Timer intervals for each exercise

    const renderedContent = computed(() => {
      if (!lesson.value?.content_md) return ''
      return marked(lesson.value.content_md)
    })

    const allExercisesCompleted = computed(() => {
      if (!lesson.value?.exercises?.length) return false
      return lesson.value.exercises.every(exercise =>
        results.value[exercise.exercise_id]?.is_correct
      )
    })

    // 新增计算属性：练习完成统计
    const completedExercises = computed(() => {
      if (!lesson.value?.exercises?.length) return 0
      return lesson.value.exercises.filter(exercise =>
        results.value[exercise.exercise_id]?.is_correct
      ).length
    })

    const exerciseCompletionPercentage = computed(() => {
      if (!lesson.value?.exercises?.length) return 0
      return Math.round((completedExercises.value / lesson.value.exercises.length) * 100)
    })

    const loadLesson = async () => {
      try {
        loading.value = true
        window.setLoading(true)
        
        const lessonId = route.params.id
        const data = await contentAPI.getLesson(lessonId)
        lesson.value = data.lesson
        console.log('DEBUG: 课程数据加载完成:', data.lesson.progress)
        
        // Initialize code storage for each exercise
        if (lesson.value.exercises) {
          lesson.value.exercises.forEach(exercise => {
            codes.value[exercise.exercise_id] = `# ${t('lesson.placeholder').replace('...', '')}\n`
            // Initialize feedback modes
            feedbackModes.value[exercise.exercise_id] = 'simple'
            activeFeedbackTabs.value[exercise.exercise_id] = 'structural'
            // Initialize time tracking
            startTimes.value[exercise.exercise_id] = null
            hasStartedCoding.value[exercise.exercise_id] = false
            currentTimes.value[exercise.exercise_id] = 0
            timers.value[exercise.exercise_id] = null
          })
        }
      } catch (error) {
        console.error('Failed to load lesson:', error)
        const message = error.response?.data?.error || t('lesson.notifications.loadingFailed')
        window.showNotification(message, 'error')
      } finally {
        loading.value = false
        window.setLoading(false)
      }
    }

    const onCodeChange = (exerciseId, newCode) => {
      // Update the code
      codes.value[exerciseId] = newCode
      
      // 如果用户修改了代码，并且当前没有在计时，则开始新的计时
      if (!hasStartedCoding.value[exerciseId]) {
        const placeholder = `# ${t('lesson.placeholder').replace('...', '')}\n`
        console.log(`DEBUG: 检查代码变化 - 新代码长度: ${newCode.trim().length}, 占位符: "${placeholder.trim()}", 新代码: "${newCode.trim()}"`)
        
        // 检查是否开始输入实际代码（不只是默认占位符）
        if (newCode.trim().length > 0 && newCode.trim() !== placeholder.trim()) {
          startTimes.value[exerciseId] = new Date()
          hasStartedCoding.value[exerciseId] = true
          startTimer(exerciseId)
          console.log(`✅ 开始做题时间: ${startTimes.value[exerciseId]}`)
        } else {
          console.log(`❌ 代码未变化或仍为占位符，不开始计时`)
        }
      } else {
        console.log(`⏰ 已在计时中，当前计时: ${currentTimes.value[exerciseId]}秒`)
      }
    }

    const startTimer = (exerciseId) => {
      // 清除可能存在的旧计时器
      if (timers.value[exerciseId]) {
        clearInterval(timers.value[exerciseId])
      }
      
      // 启动新计时器，每秒更新一次
      timers.value[exerciseId] = setInterval(() => {
        if (startTimes.value[exerciseId]) {
          const elapsed = Math.floor((Date.now() - startTimes.value[exerciseId].getTime()) / 1000)
          currentTimes.value[exerciseId] = elapsed
        }
      }, 1000)
    }

    const stopTimer = (exerciseId) => {
      if (timers.value[exerciseId]) {
        clearInterval(timers.value[exerciseId])
        timers.value[exerciseId] = null
      }
    }

    const formatTimerTime = (seconds) => {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
    }

    const runCode = async (exercise) => {
      const code = codes.value[exercise.exercise_id]
      if (!code.trim()) {
        window.showNotification(t('lesson.notifications.enterCodeFirst'), 'warning')
        return
      }

      // 点击提交时立即停止计时并计算耗时
      let solvingTimeSec = 0
      const startTime = startTimes.value[exercise.exercise_id]
      if (startTime && hasStartedCoding.value[exercise.exercise_id]) {
        solvingTimeSec = Math.round((Date.now() - startTime.getTime()) / 1000)
        console.log(`本次做题耗时: ${solvingTimeSec}秒`)
        
        // 立即停止计时器
        stopTimer(exercise.exercise_id)
      }

      try {
        codeRunning.value = true
        
        // 准备提交数据
        const submissionData = {
          exercise_id: exercise.exercise_id,
          code: code
        }
        
        // 如果有计时数据，添加到提交中（包括0秒的情况）
        if (startTime && hasStartedCoding.value[exercise.exercise_id]) {
          submissionData.solving_time_sec = solvingTimeSec
          console.log(`传递给后端的做题耗时: ${solvingTimeSec}秒`)
        }
        
        const response = await submissionAPI.submitCode(submissionData)
        
        // Backend returns {submission: {...}, result: {...}}
        const result = response.result
        result.submission = response.submission // Add submission info for analysis display
        results.value[exercise.exercise_id] = result
        
        // 根据结果处理计时状态
        if (result.is_correct) {
          // 如果答案正确，彻底停止计时
          hasStartedCoding.value[exercise.exercise_id] = false
          currentTimes.value[exercise.exercise_id] = 0
          window.showNotification(t('lesson.notifications.codeExecutedCorrectly'), 'success')
        } else {
          // 如果答案错误，重置计时状态，等待用户修改代码时重新开始计时
          hasStartedCoding.value[exercise.exercise_id] = false
          currentTimes.value[exercise.exercise_id] = 0
          startTimes.value[exercise.exercise_id] = null
          window.showNotification(t('lesson.notifications.codeNeedsModification'), 'warning')
        }
        
      } catch (error) {
        console.error('Code execution failed:', error)
        window.showNotification(t('lesson.notifications.executionFailed'), 'error')
        
        // 出错时也重置计时状态
        hasStartedCoding.value[exercise.exercise_id] = false
        currentTimes.value[exercise.exercise_id] = 0
        startTimes.value[exercise.exercise_id] = null
      } finally {
        codeRunning.value = false
      }
    }

    const canShowHints = (exercise) => {
      // Check if there are incorrect results or lesson progress indicates hints should be shown
      const result = results.value[exercise.exercise_id]
      return (result && !result.is_correct) || 
             lesson.value?.progress?.should_show_hint ||
             (result && result.hints_available)
    }

    const requestHint = async (exerciseId) => {
      try {
        hintLoading.value = true
        
        const currentHints = hints.value[exerciseId] || []
        const nextHintLevel = currentHints.length + 1
        
        const data = await submissionAPI.requestHint(exerciseId, nextHintLevel)
        
        if (!hints.value[exerciseId]) {
          hints.value[exerciseId] = []
        }
        hints.value[exerciseId].push(data.hint)
        
        window.showNotification(t('lesson.notifications.hintObtained', { level: data.hint_level, hint: data.hint }), 'info')
      } catch (error) {
        console.error('Failed to get hint:', error)
        const message = error.response?.data?.error || t('lesson.notifications.hintRequestFailed')
        window.showNotification(message, 'error')
      } finally {
        hintLoading.value = false
      }
    }

    const getStatusText = (status) => {
      return t(`lesson.statusLabels.${status}`) || t('lesson.statusLabels.unknown')
    }

    const toggleTheme = () => {
      isDarkTheme.value = !isDarkTheme.value
      // Save theme preference to localStorage
      localStorage.setItem('codeEditorTheme', isDarkTheme.value ? 'dark' : 'light')
    }

    const toggleHistory = async (exerciseId) => {
      if (histories.value[exerciseId]) {
        // Already loaded, toggle display
        histories.value[exerciseId] = null
      } else {
        // First time loading
        try {
          historyLoading.value = true
          const response = await submissionAPI.getSubmissionHistory(exerciseId)
          histories.value[exerciseId] = response.submissions
          window.showNotification(t('lesson.notifications.historyLoaded'), 'success')
        } catch (error) {
          console.error('Failed to get submission history:', error)
          const message = error.response?.data?.error || t('lesson.notifications.historyLoadFailed')
          window.showNotification(message, 'error')
        } finally {
          historyLoading.value = false
        }
      }
    }

    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleString()
    }

    const showSubmissionCode = (submission) => {
      selectedSubmission.value = submission
      showCodeModal.value = true
    }

    const closeCodeModal = () => {
      showCodeModal.value = false
      selectedSubmission.value = null
    }

    const copyToCurrentEditor = () => {
      if (selectedSubmission.value?.code) {
        // Find the exercise that this submission belongs to
        const exerciseId = selectedSubmission.value.exercise_id
        if (exerciseId && codes.value[exerciseId] !== undefined) {
          codes.value[exerciseId] = selectedSubmission.value.code
          window.showNotification(t('lesson.notifications.codeCopiedToEditor'), 'success')
          closeCodeModal()
        }
      }
    }

    // Code quality analysis related methods
    const getScoreClass = (score) => {
      if (score >= 8) return 'excellent'
      if (score >= 6) return 'good'
      if (score >= 4) return 'fair'
      return 'poor'
    }

    const getAnalysisSuggestions = (analysisResult) => {
      if (!analysisResult) return []
      
      try {
        const analysis = typeof analysisResult === 'string' ? JSON.parse(analysisResult) : analysisResult
        const suggestions = []
        
        // 🔧 修复：优先使用新的suggestions_list字段
        if (analysis.summary?.suggestions_list) {
          suggestions.push(...analysis.summary.suggestions_list)
        } else {
          // 回退到原有逻辑
          // Extract suggestions from local analysis
          if (analysis.local_analysis?.quality_issues) {
            suggestions.push(...analysis.local_analysis.quality_issues)
          }
          
          // Extract suggestions from API analysis
          if (analysis.api_analysis) {
            Object.values(analysis.api_analysis).forEach(apiResult => {
              if (apiResult && typeof apiResult === 'object') {
                // 原有的suggestions字段
                if (apiResult.suggestions) {
                  suggestions.push(...apiResult.suggestions)
                }
                
                // 🆕 新增：从AI反馈中提取建议
                if (apiResult.feedback) {
                  const feedback = apiResult.feedback
                  if (typeof feedback === 'object') {
                    // OpenAI风格的反馈
                    if (feedback.learning_suggestions) {
                      suggestions.push(feedback.learning_suggestions)
                    }
                    if (feedback.guiding_hints) {
                      suggestions.push(feedback.guiding_hints)
                    }
                    
                    // DeepSeek风格的反馈
                    if (feedback.hints_not_answers) {
                      suggestions.push(feedback.hints_not_answers)
                    }
                    if (feedback.next_steps) {
                      suggestions.push(feedback.next_steps)
                    }
                  }
                }
              }
            })
          }
        }
        
        // 过滤空值和重复项
        return [...new Set(suggestions.filter(s => s && s.trim()))]
          .slice(0, 5) // 增加显示数量到5个
      } catch (error) {
        console.warn('Failed to parse analysis result:', error)
        return ['分析结果解析失败，请查看详细反馈信息']
      }
    }

    // 🔥 NEW: Feedback comparison related methods
    const deepseekLoading = ref({});

    const toggleFeedbackMode = async (exerciseId) => {
      const currentMode = feedbackModes.value[exerciseId];
      const newMode = currentMode === 'simple' ? 'detailed' : 'simple';
      feedbackModes.value[exerciseId] = newMode;

      // If switching to detailed mode and DeepSeek data hasn't loaded yet, trigger loading
      if (newMode === 'detailed' && !isDeepSeekDataAvailable(exerciseId)) {
        deepseekLoading.value[exerciseId] = true;
        try {
          const submissionId = results.value[exerciseId].submission.submission_id;
          const response = await submissionAPI.requestAdditionalFeedback(submissionId, 'deepseek');
          
          // Merge new feedback data into existing results
          const newFeedbackData = response.feedback_content;
          const comprehensiveFeedback = results.value[exerciseId].comprehensive_feedback.comprehensive_feedback || results.value[exerciseId].comprehensive_feedback;
          
          comprehensiveFeedback.ai_generated.deepseek = {
              feedback: JSON.parse(newFeedbackData.feedback_content),
              generation_time: newFeedbackData.generation_time_ms,
              cost: newFeedbackData.api_cost,
              source: 'deepseek_educational',
              feedback_id: newFeedbackData.feedback_id
          };

          // Update UI
          window.showNotification(t('lesson.notifications.deepseekFeedbackLoaded'), 'success');
        } catch (error) {
          console.error('Failed to load DeepSeek feedback:', error);
          window.showNotification(t('lesson.notifications.deepseekFeedbackFailed'), 'error');
        } finally {
          deepseekLoading.value[exerciseId] = false;
        }
      }

      // Set default active tab
      if (newMode === 'detailed') {
        const result = results.value[exerciseId];
        if (result?.comprehensive_feedback?.recommended_primary) {
          activeFeedbackTabs.value[exerciseId] = result.comprehensive_feedback.recommended_primary;
        }
      }
    };

    const isDeepSeekDataAvailable = (exerciseId) => {
        const result = results.value[exerciseId];
        const feedbacks = result?.comprehensive_feedback?.comprehensive_feedback?.ai_generated || {};
        return feedbacks.deepseek && !feedbacks.deepseek.error;
    };

    const getAvailableFeedbacks = (exerciseId) => {
      const result = results.value[exerciseId];
      if (!result?.comprehensive_feedback) return {};
      
      const feedbacks = {};
      const comprehensiveFeedback = result.comprehensive_feedback.comprehensive_feedback || result.comprehensive_feedback;
      
      // Add structural feedback
      feedbacks['structural'] = {
        feedback: comprehensiveFeedback.structural,
        generation_time: 0,
        cost: 0
      };
      
      // Add existing AI feedback
      const aiFeedbacks = comprehensiveFeedback.ai_generated || {};
      Object.keys(aiFeedbacks).forEach(apiName => {
        feedbacks[apiName] = aiFeedbacks[apiName];
      });

      // If DeepSeek should be shown but data is not yet available, add a placeholder
      if (feedbackModes.value[exerciseId] === 'detailed' && !isDeepSeekDataAvailable(exerciseId)) {
          feedbacks['deepseek'] = { placeholder: true, loading: deepseekLoading.value[exerciseId] };
      }
      
      return feedbacks;
    };

    const getFeedbackTypeLabel = (feedbackType) => {
      return t(`lesson.feedbackTypeLabels.${feedbackType}`) || feedbackType
    }

    const getFeedbackComponent = (feedbackType) => {
      // Return different component names based on feedback type
      if (feedbackType === 'structural') {
        return 'StructuralFeedback'
      } else if (feedbackType.includes('openai')) {
        return 'AIFeedback'
      } else if (feedbackType.includes('deepseek')) {
        return 'AIFeedback'
      }
      return 'BasicFeedback'
    }

    const getActiveFeedbackData = (exerciseId) => {
      const feedbacks = getAvailableFeedbacks(exerciseId)
      const activeType = activeFeedbackTabs.value[exerciseId]
      return feedbacks[activeType] || {}
    }

    const getStageLabel = (stage) => {
      return t(`lesson.stageLabels.${stage}`) || t('lesson.stageLabels.unknown')
    }

    const feedbackSourceInfo = computed(() => (exerciseId) => {
      const feedbacks = getAvailableFeedbacks(exerciseId);
      const sources = Object.keys(feedbacks).filter(key => !feedbacks[key].placeholder);
      const sourceNames = sources.map(s => {
          if (s === 'structural') return 'Judge0';
          if (s.includes('openai')) return 'OpenAI';
          if (s.includes('deepseek')) return 'DeepSeek';
          return 'Other';
      });
      return {
          count: sources.length,
          names: `(${sourceNames.join(', ')})`
      };
    });

    const handleFeedbackRating = async (data) => {
      try {
        // Submit feedback rating
        await submissionAPI.rateFeedback(data.feedback_id, data.helpfulness_rating, data.clarity_rating)
        window.showNotification(t('lesson.notifications.feedbackRatingSuccess'), 'success')
      } catch (error) {
        console.error('Failed to submit rating:', error)
        window.showNotification(t('lesson.notifications.feedbackRatingFailed'), 'error')
      }
    }

    // 过滤可见的测试用例 (不显示隐藏的测试用例)
    const getVisibleTestCases = (exercise) => {
      if (!exercise?.test_cases) return []
      return exercise.test_cases.filter(testCase => !testCase.is_hidden)
    }

    // Restore theme preference from localStorage
    const savedTheme = localStorage.getItem('codeEditorTheme')
    if (savedTheme) {
      isDarkTheme.value = savedTheme === 'dark'
    }

    onMounted(() => {
      loadLesson()
    })

    // 组件卸载时清理所有计时器
    onUnmounted(() => {
      Object.keys(timers.value).forEach(exerciseId => {
        if (timers.value[exerciseId]) {
          clearInterval(timers.value[exerciseId])
        }
      })
    })

    return {
      t,
      lesson,
      loading,
      codeRunning,
      hintLoading,
      isDarkTheme,
      codes,
      results,
      hints,
      // NEW: Submission history functionality
      histories,
      historyLoading,
      toggleHistory,
      formatTime,
      // NEW: Code modal functionality
      showCodeModal,
      selectedSubmission,
      showSubmissionCode,
      closeCodeModal,
      copyToCurrentEditor,
      renderedContent,
      allExercisesCompleted,
      // NEW: Exercise completion tracking
      completedExercises,
      exerciseCompletionPercentage,
      loadLesson,
      onCodeChange,
      runCode,
      canShowHints,
      requestHint,
      getStatusText,
      toggleTheme,
      getScoreClass,
      getAnalysisSuggestions,
      // 🔥 NEW: Feedback comparison functionality
      feedbackModes,
      activeFeedbackTabs,
      toggleFeedbackMode,
      getAvailableFeedbacks,
      getFeedbackTypeLabel,
      getFeedbackComponent,
      getActiveFeedbackData,
      getStageLabel,
      handleFeedbackRating,
      deepseekLoading,
      feedbackSourceInfo,
      // 🔥 NEW: Time tracking functionality
      startTimes,
      hasStartedCoding,
      currentTimes,
      formatTimerTime,
      // 🔥 NEW: Test case visibility filtering
      getVisibleTestCases
    }
  }
}
</script>

<style scoped>
.lesson-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 1rem 2rem 1rem;
  background-color: var(--color-slate-50);
  min-height: 100vh;
}

/* 现代化卡片样式 */
.card, .modern-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 1.5rem;
  border: 1px solid var(--color-slate-200);
  position: relative;
  overflow: hidden;
}

.card:hover, .modern-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  border-color: var(--color-slate-300);
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-blue-500), var(--color-purple-500));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modern-card:hover::before {
  opacity: 1;
}

.modern-card.completed::before {
  background: linear-gradient(90deg, var(--color-green-500), var(--color-blue-500));
  opacity: 1;
}

/* 现代化导航样式 */
.lesson-nav {
  padding: 2rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
  backdrop-filter: blur(10px);
}

.nav-content {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.nav-left {
  flex-shrink: 0;
}

.lesson-title {
  flex: 1;
}

.lesson-title-text {
  color: var(--color-slate-900);
  margin: 0;
  font-size: 2rem;
  font-weight: 800;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, var(--color-slate-900), var(--color-slate-700));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.lesson-progress {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.progress-badge-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.lesson-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--color-slate-600);
  background: var(--color-slate-100);
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  border: 1px solid var(--color-slate-200);
}

.meta-icon {
  width: 16px;
  height: 16px;
  opacity: 0.7;
}

/* 进度条样式 */
.lesson-progress-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--color-slate-200);
}

.progress-track {
  flex: 1;
  height: 8px;
  background: var(--color-slate-200);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-blue-500), var(--color-purple-500));
  border-radius: 4px;
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-text {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-slate-700);
  white-space: nowrap;
}

/* 现代化徽章样式 */
.progress-badge, .modern-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.badge-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.progress-badge.not_started .badge-indicator {
  background: var(--color-slate-400);
}

.progress-badge.in_progress .badge-indicator {
  background: var(--color-amber-500);
}

.progress-badge.completed .badge-indicator {
  background: var(--color-green-500);
}

.progress-badge.not_started {
  background: var(--color-slate-50);
  color: var(--color-slate-700);
  border: 1px solid var(--color-slate-200);
}

.progress-badge.in_progress {
  background: linear-gradient(135deg, var(--color-amber-50), var(--color-amber-100));
  color: var(--color-amber-800);
  border: 1px solid var(--color-amber-300);
}

.progress-badge.completed {
  background: linear-gradient(135deg, var(--color-green-50), var(--color-green-100));
  color: var(--color-green-800);
  border: 1px solid var(--color-green-300);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.attempts-text {
  font-size: 0.875rem;
  color: var(--color-slate-500);
}

.avg-time-text {
  font-size: 0.875rem;
  color: var(--color-blue-600);
  font-weight: 500;
  background: var(--color-blue-50);
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  border: 1px solid var(--color-blue-200);
}

/* 现代化按钮样式 */
.btn {
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: 1px solid transparent;
  font-size: 0.875rem;
  position: relative;
  overflow: hidden;
  text-decoration: none;
  user-select: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-with-icon .icon {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.btn:hover .icon {
  transform: scale(1.1);
}

.btn-secondary {
  background: var(--color-slate-100);
  color: var(--color-slate-700);
  border-color: var(--color-slate-200);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-slate-200);
  border-color: var(--color-slate-300);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-success {
  background: linear-gradient(135deg, var(--color-green-500), var(--color-green-600));
  color: white;
  border-color: var(--color-green-500);
}

.btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-green-600), var(--color-green-700));
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(34, 197, 94, 0.3);
}

.btn-warning {
  background: var(--color-amber-50);
  color: var(--color-amber-700);
  border-color: var(--color-amber-200);
}

.btn-warning:hover:not(:disabled) {
  background: var(--color-amber-100);
  border-color: var(--color-amber-300);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
}

.btn-ghost {
  background: transparent;
  color: var(--color-slate-600);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background: var(--color-slate-100);
  color: var(--color-slate-800);
}

/* 加载状态 */
.btn-loading {
  pointer-events: none;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.lesson-content {
  display: grid;
  gap: 30px;
}

.content-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid var(--color-slate-200);
}

.content-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 采用简洁现代的设计风格 - 使用:deep()确保样式应用到v-html内容 */
.markdown-content {
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.7;
  color: var(--color-slate-600);
  font-size: 1rem;
}

/* 标题样式 - 简洁风格 */
.markdown-content :deep(h1) {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--color-slate-900);
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--color-blue-600);
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.markdown-content :deep(h1:first-child) {
  margin-top: 0;
}

.markdown-content :deep(h2) {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-slate-700);
  margin: 2rem 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--color-blue-600);
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.markdown-content :deep(h3) {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-slate-700);
  margin: 1.5rem 0 0.75rem 0;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.markdown-content :deep(h4) {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-slate-700);
  margin: 1.25rem 0 0.75rem 0;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 段落样式 */
.markdown-content :deep(p) {
  margin-bottom: 1.5rem;
  line-height: 1.7;
  color: var(--color-slate-600);
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 代码块样式 - 采用深色主题 */
.markdown-content :deep(pre) {
  background-color: var(--color-slate-800);
  color: var(--color-slate-400);
  font-family: 'Fira Code', 'Consolas', monospace;
  padding: 1.5rem;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre;
  overflow-x: auto;
  margin: 1.5rem 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.markdown-content :deep(pre code) {
  background: none !important;
  color: inherit;
  padding: 0;
  font-size: inherit;
  border-radius: 0;
}

/* 行内代码样式 */
.markdown-content :deep(code) {
  background-color: var(--color-slate-100);
  color: var(--color-red-600);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Fira Code', 'Consolas', monospace;
  font-size: 0.875rem;
  font-weight: 500;
}

/* 列表样式 - 简洁风格 */
.markdown-content :deep(ul), .markdown-content :deep(ol) {
  margin: 1.5rem 0;
  padding-left: 1.5rem;
}

.markdown-content :deep(ul li), .markdown-content :deep(ol li) {
  margin-bottom: 0.5rem;
  color: var(--color-slate-600);
  line-height: 1.6;
}

.markdown-content :deep(ul) {
  list-style-type: disc;
}

.markdown-content :deep(ol) {
  list-style-type: decimal;
}

/* 引用块样式 */
.markdown-content :deep(blockquote) {
  background-color: var(--color-slate-50);
  border-left: 4px solid var(--color-blue-600);
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  border-radius: 8px;
  color: var(--color-slate-600);
  font-style: italic;
}

.markdown-content :deep(blockquote p) {
  margin: 0;
}

/* 表格样式 */
.markdown-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  overflow: hidden;
}

.markdown-content :deep(th) {
  background-color: var(--color-slate-50);
  color: var(--color-slate-700);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  border-bottom: 1px solid var(--color-slate-200);
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.markdown-content :deep(td) {
  padding: 1rem;
  border-bottom: 1px solid var(--color-slate-200);
  background-color: white;
  color: var(--color-slate-600);
}

.markdown-content :deep(tr:nth-child(even) td) {
  background-color: var(--color-slate-50);
}

.markdown-content :deep(tr:hover td) {
  background-color: var(--color-blue-100);
}

/* 强调文本 */
.markdown-content :deep(strong) {
  color: var(--color-slate-700);
  font-weight: 700;
}

.markdown-content :deep(em) {
  color: var(--color-slate-600);
  font-style: italic;
}

/* 链接样式 */
.markdown-content :deep(a) {
  color: var(--color-blue-600);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.markdown-content :deep(a:hover) {
  color: var(--color-blue-700);
  text-decoration: underline;
}

/* 分割线 */
.markdown-content :deep(hr) {
  border: none;
  height: 1px;
  background-color: var(--color-slate-200);
  margin: 2rem 0;
}

/* 图片样式 */
.markdown-content :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 1.5rem 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lesson-container {
    padding: 0.5rem;
  }
  
  .lesson-nav {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .lesson-title h1 {
    font-size: 1.5rem;
  }
  
  .content-section {
    padding: 1.5rem;
  }
  
  .markdown-content {
    font-size: 0.9rem;
  }
  
  .markdown-content h1 {
    font-size: 1.5rem;
  }
  
  .markdown-content h2 {
    font-size: 1.25rem;
  }
  
  .markdown-content h3 {
    font-size: 1.125rem;
  }
}

/* 现代化练习部分样式 */
.exercises-section {
  margin-top: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--color-slate-200);
}

.section-title {
  color: var(--color-slate-900);
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, var(--color-slate-900), var(--color-blue-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.exercise-count {
  background: var(--color-blue-50);
  color: var(--color-blue-700);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  border: 1px solid var(--color-blue-200);
}

.exercises-grid {
  display: grid;
  gap: 2rem;
}

.exercise-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  margin-bottom: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--color-slate-200);
  position: relative;
}

.exercise-card.completed {
  border-color: var(--color-green-300);
  background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(240, 253, 244, 0.5));
}

.exercise-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.exercise-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.exercise-number {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--color-blue-500), var(--color-purple-500));
  color: white;
  border-radius: 12px;
  font-weight: 700;
  font-size: 1.125rem;
  flex-shrink: 0;
}

.exercise-number .number {
  z-index: 2;
}

.completion-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  background: var(--color-green-500);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  z-index: 3;
}

.check-icon {
  width: 12px;
  height: 12px;
  color: white;
}

.exercise-title {
  color: var(--color-slate-800);
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.exercise-actions {
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
  flex-shrink: 0;
}

.problem-statement {
  background: var(--color-slate-50);
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid var(--color-blue-600);
  margin-bottom: 20px;
}

.hints-section {
  background: var(--color-amber-50);
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid var(--color-amber-500);
  margin-bottom: 20px;
}

.hints-section h4 {
  color: var(--color-amber-800);
  margin-bottom: 12px;
}

.hint-item {
  margin-bottom: 8px;
  color: var(--color-amber-800);
}

/* Submission History Styles */
.history-section {
  background: var(--color-blue-50);
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid var(--color-blue-600);
  margin-bottom: 20px;
}

.history-section h4 {
  color: var(--color-blue-800);
  margin-bottom: 12px;
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  background: white;
  padding: 10px 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  border-left: 3px solid var(--color-slate-500);
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.history-item.clickable {
  cursor: pointer;
}

.history-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transform: translateY(-1px);
}

.history-item.clickable:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transform: translateY(-2px);
  background: linear-gradient(to right, var(--color-slate-50), white);
}

.history-item.success {
  border-left-color: var(--color-green-600);
  background: linear-gradient(to right, var(--color-green-50), white);
}

.history-time {
  color: var(--color-slate-500);
  font-size: 12px;
  min-width: 120px;
}

.history-status {
  font-size: 16px;
  min-width: 24px;
}

.history-attempts {
  color: var(--color-slate-600);
  font-weight: 500;
  min-width: 80px;
}

.history-error {
  color: var(--color-red-500);
  font-size: 12px;
  font-style: italic;
  opacity: 0.8;
}

.exercise-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.history-hint {
  margin-left: auto;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.history-item.clickable:hover .history-hint {
  opacity: 1;
}

/* Code Modal Styles */
.code-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.code-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.code-modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--color-slate-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--color-slate-50);
}

.code-modal-header h3 {
  margin: 0;
  color: var(--color-slate-800);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--color-slate-500);
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: var(--color-slate-200);
  color: var(--color-slate-600);
}

.code-modal-info {
  padding: 16px 24px;
  background: var(--color-slate-50);
  border-bottom: 1px solid var(--color-slate-200);
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.modal-time {
  color: var(--color-slate-500);
  font-size: 14px;
}

.modal-status {
  font-weight: 500;
}

.modal-error {
  color: var(--color-red-500);
  font-size: 14px;
  font-style: italic;
}

.code-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  min-height: 200px;
  max-height: 400px;
}

.code-modal-content pre {
  background: var(--color-slate-50);
  border: 1px solid var(--color-slate-200);
  border-radius: 8px;
  padding: 16px;
  margin: 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: var(--color-slate-800);
}

.code-modal-content code {
  background: none;
  padding: 0;
  border-radius: 0;
  font-family: inherit;
  color: inherit;
}

.code-modal-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--color-slate-200);
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  background: var(--color-slate-50);
}

/* Responsive design for modal */
@media (max-width: 768px) {
  .code-modal {
    width: 95vw;
    max-height: 85vh;
  }
  
  .code-modal-header,
  .code-modal-info,
  .code-modal-content,
  .code-modal-footer {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .code-modal-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* 现代化代码编辑器样式 */
.code-editor-section {
  margin: 2rem 0;
  border-radius: 16px;
  overflow: hidden;
  background: white;
  border: 1px solid var(--color-slate-200);
  transition: all 0.3s ease;
}

.code-editor-section:hover {
  border-color: var(--color-blue-300);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.1);
}

.editor-header {
  background: linear-gradient(135deg, var(--color-slate-50), var(--color-slate-100));
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--color-slate-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.editor-title-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex: 1;
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  color: var(--color-slate-700);
  font-size: 1rem;
}

.editor-icon {
  width: 20px;
  height: 20px;
  color: var(--color-blue-600);
}

.coding-timer {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.timer-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, var(--color-blue-50), var(--color-blue-100));
  padding: 0.5rem 1rem;
  border-radius: 12px;
  border: 1px solid var(--color-blue-200);
  animation: pulse-timer 2s infinite;
}

@keyframes pulse-timer {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

.timer-icon {
  width: 16px;
  height: 16px;
  color: var(--color-blue-600);
}

.timer-text {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.875rem;
  font-weight: 700;
  color: var(--color-blue-700);
  letter-spacing: 0.5px;
}

.editor-controls {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.code-editor-wrapper {
  background: white;
  position: relative;
}

.code-editor-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--color-blue-300), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.code-editor-section:hover .code-editor-wrapper::before {
  opacity: 1;
}

.timer-text {
  font-weight: 600;
  font-family: 'Courier New', monospace;
  letter-spacing: 0.5px;
}

@keyframes pulse {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.8; 
    transform: scale(1.02);
  }
}

.editor-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.code-editor {
  border-radius: 0 0 8px 8px;
  overflow: hidden;
}

.result-section {
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.result-header {
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
}

.result-header.success {
  background: var(--color-green-50);
  color: var(--color-green-800);
}

.result-header.error {
  background: var(--color-red-50);
  color: var(--color-red-800);
}

.result-icon {
  font-size: 20px;
}

.feedback-message {
  padding: 16px;
  background: white;
  border-left: 4px solid var(--color-slate-300);
}

.error-details {
  background: var(--color-slate-50);
  padding: 16px;
}

.error-message, .output-section, .expected-output {
  margin-bottom: 16px;
}

.error-details pre {
  background: white;
  padding: 12px;
  border-radius: 4px;
  margin-top: 8px;
  white-space: pre-wrap;
  font-size: 14px;
}

.hint-reminder {
  background: var(--color-blue-50);
  color: var(--color-blue-700);
  padding: 12px 16px;
  border-radius: 6px;
  margin-top: 12px;
}

/* Code quality analysis styles */
.code-quality-section {
  background: var(--color-slate-50);
  border: 1px solid var(--color-slate-200);
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.code-quality-section h4 {
  margin: 0 0 12px 0;
  color: var(--color-slate-800);
}

.quality-metrics {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.metric {
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-label {
  font-weight: 500;
  color: var(--color-slate-600);
}

.metric-value {
  font-weight: 600;
  color: var(--color-slate-800);
}

.quality-score.excellent {
  color: var(--color-green-700);
}

.quality-score.good {
  color: var(--color-green-600);
}

.quality-score.fair {
  color: var(--color-amber-600);
}

.quality-score.poor {
  color: var(--color-red-600);
}

/* 🔧 修复：独立的改进建议板块样式 */
.improvement-suggestions-section {
  background: var(--color-slate-50);
  border: 1px solid var(--color-slate-200);
  padding: 16px;
  border-radius: 8px;
  margin-top: 16px;
}

.improvement-suggestions-section h4 {
  margin: 0 0 16px 0;
  color: var(--color-slate-800);
  display: flex;
  align-items: center;
  font-size: 18px;
}

/* 移除标题前的💡，因为翻译文本中已包含 */

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.suggestion-item {
  background: white;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid var(--color-blue-600);
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.suggestion-text {
  color: var(--color-slate-600);
  font-size: 14px;
  line-height: 1.5;
  display: block;
  width: 100%;
}

.suggestion-icon {
  font-size: 16px;
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
}

.no-suggestions {
  display: flex;
  align-items: center;
  background: white;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid var(--color-slate-500);
  font-style: italic;
}

.test-cases-section {
  background: var(--color-slate-50);
  padding: 16px;
  border-radius: 8px;
  margin-top: 20px;
}

.test-cases-section h4 {
  color: var(--color-slate-800);
  margin-bottom: 12px;
}

.test-case {
  background: white;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  border-left: 3px solid var(--color-blue-600);
}

.completion-banner {
  background: linear-gradient(135deg, var(--color-blue-500) 0%, var(--color-blue-600) 100%);
  color: white;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  margin-top: 30px;
}

.completion-content h3 {
  margin-bottom: 16px;
}

.completion-content p {
  margin-bottom: 24px;
  opacity: 0.9;
}

.completion-content .btn {
  background: white;
  color: var(--color-blue-500);
}

.loading-container, .error-container {
  text-align: center;
  padding: 60px 20px;
}

.loading-container .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-slate-200);
  border-top: 4px solid var(--color-blue-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 🔥 NEW: Feedback comparison functionality styles */
.feedback-controls {
  margin-left: auto;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.comprehensive-feedback-section {
  background: var(--color-slate-50);
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.feedback-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.feedback-tab {
  padding: 8px 16px;
  border: 1px solid var(--color-slate-300);
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.feedback-tab:hover {
  background: var(--color-slate-200);
  border-color: var(--color-slate-400);
}

.feedback-tab.active {
  background: var(--color-blue-600);
  color: white;
  border-color: var(--color-blue-600);
}

.feedback-tab.disabled {
  cursor: not-allowed;
  background: var(--color-slate-50);
  color: var(--color-slate-500);
}

.spinner-sm {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 0.6s linear infinite;
  margin-right: 5px;
}

.generation-time {
  font-size: 12px;
  opacity: 0.8;
  font-weight: normal;
}

.feedback-content-area {
  display: grid;
  gap: 16px;
}

.active-feedback-content {
  background: white;
  border-radius: 6px;
  padding: 16px;
  border-left: 4px solid var(--color-blue-600);
}

.feedback-insights {
  background: var(--color-blue-50);
  border-radius: 6px;
  padding: 16px;
  border-left: 4px solid #17a2b8;
}

.feedback-insights h5 {
  margin: 0 0 12px 0;
  color: #0c5460;
}

.insights-content p {
  margin: 8px 0;
  font-size: 14px;
  color: #0c5460;
}

.insights-content strong {
  color: var(--color-slate-600);
}

.source-names {
    font-size: 13px;
    color: var(--color-slate-500);
    margin-left: 4px;
}

/* Responsive design */
@media (max-width: 768px) {
  .feedback-tabs {
    flex-direction: column;
  }
  
  .feedback-tab {
    width: 100%;
    justify-content: center;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .feedback-controls {
    margin-left: 0;
    width: 100%;
  }
}
</style>