<template>
  <div class="lesson-container" v-if="lesson">
    <!-- Lesson Navigation -->
    <div class="lesson-nav card">
      <div class="nav-content">
        <div class="nav-left">
          <button @click="$router.go(-1)" class="btn btn-secondary">
            {{ $t('lesson.backToCourseList') }}
          </button>
        </div>
        <div class="lesson-title">
          <h1>{{ lesson.title }}</h1>
          <div class="lesson-progress">
            <span class="progress-badge" :class="lesson.progress?.status">
              {{ getStatusText(lesson.progress?.status) }}
            </span>
            <span v-if="lesson.progress?.attempts" class="attempts-text">
              {{ $t('lesson.attemptCount', { count: lesson.progress.attempts }) }}
            </span>
            <span v-if="lesson.progress?.avg_time_sec && lesson.progress.avg_time_sec > 0" class="avg-time-text">
              📊 {{ $t('lesson.avgSolvingTime') }}: {{ formatTimerTime(Math.round(lesson.progress.avg_time_sec)) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Lesson Content Area -->
    <div class="lesson-content">
      <!-- Theoretical Content -->
      <div class="content-section">
        <div class="markdown-content" v-html="renderedContent"></div>
      </div>

      <!-- Programming Exercises -->
      <div v-if="lesson.exercises?.length" class="exercises-section">
        <h2>{{ $t('lesson.programmingExercises') }}</h2>
        <div 
          v-for="(exercise, index) in lesson.exercises" 
          :key="exercise.exercise_id"
          class="exercise-card"
        >
          <div class="exercise-header">
            <h3>{{ $t('lesson.exerciseNumber', { number: index + 1 }) }}</h3>
            <div class="exercise-actions">
              <button 
                v-if="canShowHints(exercise)"
                @click="requestHint(exercise.exercise_id)"
                class="btn btn-warning btn-sm"
                :disabled="hintLoading"
              >
                {{ $t('lesson.getHint') }}
              </button>
              <button 
                @click="toggleHistory(exercise.exercise_id)"
                class="btn btn-info btn-sm"
                :disabled="historyLoading"
              >
                📊 {{ $t('lesson.viewHistory') }}
              </button>
            </div>
          </div>

          <div class="exercise-content">
            <div class="problem-statement">
              <p>{{ exercise.problem_statement }}</p>
            </div>

            <!-- Hints Display -->
            <div v-if="hints[exercise.exercise_id]" class="hints-section">
              <h4>{{ $t('lesson.hints') }}</h4>
              <div 
                v-for="(hint, hintIndex) in hints[exercise.exercise_id]" 
                :key="hintIndex"
                class="hint-item"
              >
                <strong>{{ $t('lesson.hintNumber', { number: hintIndex + 1 }) }}</strong> {{ hint }}
              </div>
            </div>

            <!-- Submission History Display -->
            <div v-if="histories[exercise.exercise_id]" class="history-section">
              <h4>{{ $t('lesson.submissionHistory') }}</h4>
              <div class="history-list">
                <div 
                  v-for="(submission, historyIndex) in histories[exercise.exercise_id]" 
                  :key="submission.submission_id"
                  class="history-item clickable"
                  :class="{ 'success': submission.is_correct }"
                  @click="showSubmissionCode(submission)"
                  :title="$t('lesson.clickToViewCode')"
                >
                  <span class="history-time">{{ formatTime(submission.timestamp) }}</span>
                  <span class="history-status">{{ submission.is_correct ? '✅' : '❌' }}</span>
                  <span class="history-attempts">{{ $t('lesson.attempt', { number: histories[exercise.exercise_id].length - historyIndex }) }}</span>
                  <span v-if="submission.error_type && !submission.is_correct" class="history-error">
                    ({{ $t(`lesson.errorTypes.${submission.error_type}`) || submission.error_type }})
                  </span>
                  <span class="history-hint">👁️</span>
                </div>
              </div>
            </div>

            <!-- Code Editor -->
            <div class="code-editor-section">
              <div class="editor-header">
                <div class="editor-title">
                  <span>{{ $t('lesson.pythonCodeEditor') }}</span>
                  <!-- 计时器显示 -->
                  <div v-if="hasStartedCoding[exercise.exercise_id]" class="coding-timer">
                    <i class="timer-icon">⏱️</i>
                    <span class="timer-text">{{ formatTimerTime(currentTimes[exercise.exercise_id] || 0) }}</span>
                  </div>
                </div>
                <div class="editor-controls">
                  <button 
                    @click="toggleTheme"
                    class="btn btn-secondary btn-sm"
                    :title="$t('lesson.toggleTheme')"
                  >
                    {{ isDarkTheme ? '🌞' : '🌙' }}
                  </button>
                  <button 
                    @click="runCode(exercise)"
                    class="btn btn-success"
                    :disabled="codeRunning"
                  >
                    {{ codeRunning ? $t('lesson.running') : '▶ ' + $t('lesson.runCode') }}
                  </button>
                </div>
              </div>
              <div class="code-editor">
                <CodeEditor
                  v-model="codes[exercise.exercise_id]"
                  :theme="isDarkTheme ? 'dark' : 'light'"
                  height="250px"
                  :placeholder="$t('lesson.placeholder')"
                  @update:modelValue="onCodeChange(exercise.exercise_id, $event)"
                />
              </div>
            </div>

            <!-- Execution Results -->
            <div v-if="results[exercise.exercise_id]" class="result-section">
              <div class="result-header" :class="results[exercise.exercise_id].is_correct ? 'success' : 'error'">
                <span class="result-icon">
                  {{ results[exercise.exercise_id].is_correct ? '✅' : '❌' }}
                </span>
                <span class="result-text">
                  {{ results[exercise.exercise_id].is_correct ? $t('lesson.codeCorrect') : $t('lesson.codeNeedsImprovement') }}
                </span>
                <!-- 🔥 NEW: Feedback Comparison Toggle Button -->
                <div class="feedback-controls" v-if="results[exercise.exercise_id].comprehensive_feedback?.comprehensive_feedback || results[exercise.exercise_id].comprehensive_feedback?.ai_generated">
                  <button 
                    @click="toggleFeedbackMode(exercise.exercise_id)"
                    class="btn btn-sm btn-secondary"
                    :title="feedbackModes[exercise.exercise_id] === 'simple' ? $t('lesson.viewDetailedComparison') : $t('lesson.simpleView')"
                  >
                    {{ feedbackModes[exercise.exercise_id] === 'simple' ? $t('lesson.viewComparison') : $t('lesson.simpleMode') }}
                  </button>
                </div>
              </div>
              
              <!-- Simple Feedback Mode -->
              <div v-if="feedbackModes[exercise.exercise_id] === 'simple'" class="feedback-message">
                {{ results[exercise.exercise_id].feedback }}
              </div>

              <!-- 🔥 NEW: Detailed Feedback Comparison Mode -->
              <div v-else-if="results[exercise.exercise_id].comprehensive_feedback?.comprehensive_feedback || results[exercise.exercise_id].comprehensive_feedback?.ai_generated" class="comprehensive-feedback-section">
                <div class="feedback-tabs">
                  <button 
                    v-for="(feedbackData, feedbackType) in getAvailableFeedbacks(exercise.exercise_id)"
                    :key="feedbackType"
                    @click="!feedbackData.placeholder && (activeFeedbackTabs[exercise.exercise_id] = feedbackType)"
                    class="feedback-tab"
                    :class="{ 
                      active: activeFeedbackTabs[exercise.exercise_id] === feedbackType,
                      disabled: feedbackData.placeholder
                    }"
                    :disabled="feedbackData.placeholder"
                  >
                    <span v-if="feedbackData.loading" class="spinner-sm"></span>
                    {{ getFeedbackTypeLabel(feedbackType) }}
                    <span class="generation-time" v-if="feedbackData.generation_time">
                      ({{ Math.round(feedbackData.generation_time) }}ms)
                    </span>
                  </button>
                </div>
                
                <div class="feedback-content-area">
                  <div class="active-feedback-content">
                    <FeedbackComponents
                      :feedback-data="getActiveFeedbackData(exercise.exercise_id)"
                      :submission-id="results[exercise.exercise_id].submission?.submission_id"
                      :current-component="getFeedbackComponent(activeFeedbackTabs[exercise.exercise_id])"
                      @rate-feedback="handleFeedbackRating"
                    />
                  </div>
                  
                  <!-- Feedback Comparison Insights -->
                  <div class="feedback-insights" v-if="(results[exercise.exercise_id].comprehensive_feedback?.comprehensive_feedback || results[exercise.exercise_id].comprehensive_feedback)?.comparison">
                    <h5>{{ $t('lesson.feedbackComparison') }}</h5>
                    <div class="insights-content">
                      <p><strong>{{ $t('lesson.feedbackSources') }}</strong> {{ feedbackSourceInfo(exercise.exercise_id).count }} <span class="source-names">{{ feedbackSourceInfo(exercise.exercise_id).names }}</span></p>
                      <p><strong>{{ $t('lesson.recommendedPrimaryFeedback') }}</strong> {{ getFeedbackTypeLabel((results[exercise.exercise_id].comprehensive_feedback?.comprehensive_feedback || results[exercise.exercise_id].comprehensive_feedback)?.recommended_primary) }}</p>
                      <p><strong>{{ $t('lesson.learningStage') }}</strong> {{ getStageLabel((results[exercise.exercise_id].comprehensive_feedback?.comprehensive_feedback || results[exercise.exercise_id].comprehensive_feedback)?.learning_stage_adapted?.stage) }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Error Information -->
              <div v-if="!results[exercise.exercise_id].is_correct" class="error-details">
                <div v-if="results[exercise.exercise_id].error_message" class="error-message">
                  <strong>{{ $t('lesson.errorMessage') }}</strong>
                  <pre>{{ results[exercise.exercise_id].error_message }}</pre>
                </div>
                
                <div v-if="results[exercise.exercise_id].output" class="output-section">
                  <strong>{{ $t('lesson.yourOutput') }}:</strong>
                  <pre>{{ results[exercise.exercise_id].output }}</pre>
                </div>
                
                <div v-if="results[exercise.exercise_id].expected_output" class="expected-output">
                  <strong>{{ $t('lesson.expectedOutput') }}:</strong>
                  <pre>{{ results[exercise.exercise_id].expected_output }}</pre>
                </div>
              </div>

              <!-- Code Quality Analysis -->
              <div v-if="results[exercise.exercise_id].submission" class="code-quality-section">
                <h4>{{ $t('lesson.codeQualityAnalysis') }}</h4>
                <div class="quality-metrics">
                  <div class="metric" v-if="results[exercise.exercise_id].submission.code_lines">
                    <span class="metric-label">{{ $t('lesson.linesOfCode') }}</span>
                    <span class="metric-value">{{ results[exercise.exercise_id].submission.code_lines }}</span>
                  </div>
                  <div class="metric" v-if="results[exercise.exercise_id].submission.code_complexity">
                    <span class="metric-label">{{ $t('lesson.complexity') }}:</span>
                    <span class="metric-value">{{ results[exercise.exercise_id].submission.code_complexity.toFixed(1) }}</span>
                  </div>
                  <div class="metric" v-if="results[exercise.exercise_id].submission.syntax_score">
                    <span class="metric-label">{{ $t('lesson.qualityScore') }}</span>
                    <span class="metric-value quality-score" :class="getScoreClass(results[exercise.exercise_id].submission.syntax_score)">
                      {{ results[exercise.exercise_id].submission.syntax_score.toFixed(1) }}/10
                    </span>
                  </div>
                </div>
              </div>

              <!-- 🔧 修复：独立的改进建议板块 -->
              <div v-if="results[exercise.exercise_id].submission?.api_analysis_result" class="improvement-suggestions-section">
                <h4>{{ $t('lesson.improvementSuggestions') }}</h4>
                <div class="suggestions-list">
                  <div v-for="suggestion in getAnalysisSuggestions(results[exercise.exercise_id].submission.api_analysis_result)" 
                       :key="suggestion"
                       class="suggestion-item">
                    <span class="suggestion-text">{{ suggestion }}</span>
                  </div>
                </div>
                <div v-if="getAnalysisSuggestions(results[exercise.exercise_id].submission.api_analysis_result).length === 0" class="no-suggestions">
                  <span class="suggestion-icon">ℹ️</span>
                  <span class="suggestion-text">{{ $t('lesson.noSuggestionsAvailable') }}</span>
                </div>
              </div>

              <!-- Hint Availability Reminder -->
              <div v-if="!results[exercise.exercise_id].is_correct && results[exercise.exercise_id].hints_available" class="hint-reminder">
                {{ $t('lesson.hintReminder') }}
              </div>
            </div>

            <!-- Test Cases Display -->
            <div v-if="getVisibleTestCases(exercise).length" class="test-cases-section">
              <h4>{{ $t('lesson.testCases') }}</h4>
              <div 
                v-for="(testCase, testIndex) in getVisibleTestCases(exercise)" 
                :key="testIndex"
                class="test-case"
              >
                <div><strong>{{ $t('lesson.input') }}</strong> {{ testCase.input_data || $t('lesson.noInput') }}</div>
                <div><strong>{{ $t('lesson.expectedOutput') }}:</strong> {{ testCase.expected_output }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Course Completion Notification -->
    <div v-if="allExercisesCompleted" class="completion-banner">
      <div class="completion-content">
        <h3>{{ $t('lesson.congratulationsCompleted') }}</h3>
        <p>{{ $t('lesson.masteredAllConcepts') }}</p>
        <router-link to="/modules" class="btn btn-primary">
          {{ $t('lesson.continueToNextCourse') }}
        </router-link>
      </div>
    </div>
  </div>

  <div v-else-if="loading" class="loading-container">
    <div class="spinner"></div>
    <p>{{ $t('lesson.loadingContent') }}</p>
  </div>

  <div v-else class="error-container">
    <h3>{{ $t('lesson.loadFailed') }}</h3>
    <p>{{ $t('lesson.loadFailedMessage') }}</p>
    <button @click="loadLesson" class="btn btn-primary">{{ $t('lesson.reload') }}</button>
  </div>

  <!-- Submission Code Modal -->
  <div v-if="showCodeModal" class="code-modal-overlay" @click="closeCodeModal">
    <div class="code-modal" @click.stop>
      <div class="code-modal-header">
        <h3>{{ $t('lesson.submissionCodeTitle') }}</h3>
        <button class="close-btn" @click="closeCodeModal">×</button>
      </div>
      <div class="code-modal-info">
        <span class="modal-time">{{ formatTime(selectedSubmission?.timestamp) }}</span>
        <span class="modal-status">{{ selectedSubmission?.is_correct ? '✅ ' + $t('lesson.success') : '❌ ' + $t('lesson.failed') }}</span>
        <span v-if="selectedSubmission?.error_type && !selectedSubmission?.is_correct" class="modal-error">
          ({{ $t(`lesson.errorTypes.${selectedSubmission.error_type}`) || selectedSubmission.error_type }})
        </span>
      </div>
      <div class="code-modal-content">
        <pre><code>{{ selectedSubmission?.code || $t('lesson.noCodeAvailable') }}</code></pre>
      </div>
      <div class="code-modal-footer">
        <button class="btn btn-secondary" @click="closeCodeModal">{{ $t('common.close') }}</button>
        <button v-if="selectedSubmission?.code" class="btn btn-primary" @click="copyToCurrentEditor">
          {{ $t('lesson.copyToEditor') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { contentAPI, submissionAPI } from '../api.js'
import { marked } from 'marked'
import CodeEditor from '../components/CodeEditor.vue'
import FeedbackComponents from '../components/FeedbackComponents.vue'

export default {
  name: 'Lesson',
  components: {
    CodeEditor,
    FeedbackComponents
  },
  setup() {
    const route = useRoute()
    const { t } = useI18n()
    const lesson = ref(null)
    const loading = ref(false)
    const codeRunning = ref(false)
    const hintLoading = ref(false)
    const isDarkTheme = ref(false)
    
    // Store code, results and hints for each exercise
    const codes = ref({})
    const results = ref({})
    const hints = ref({})
    
    // Submission history for each exercise
    const histories = ref({})
    const historyLoading = ref(false)
    
    // Code modal for viewing submission code
    const showCodeModal = ref(false)
    const selectedSubmission = ref(null)
    
    // 🔥 NEW: Feedback mode and tab management
    const feedbackModes = ref({}) // 'simple' or 'detailed'
    const activeFeedbackTabs = ref({}) // Currently active feedback tab
    
    // 🔥 NEW: Time tracking for problem-solving duration
    const startTimes = ref({}) // Track when user starts working on each exercise
    const hasStartedCoding = ref({}) // Track if user has started typing code
    const currentTimes = ref({}) // Current elapsed time for each exercise (in seconds)
    const timers = ref({}) // Timer intervals for each exercise

    const renderedContent = computed(() => {
      if (!lesson.value?.content_md) return ''
      return marked(lesson.value.content_md)
    })

    const allExercisesCompleted = computed(() => {
      if (!lesson.value?.exercises?.length) return false
      return lesson.value.exercises.every(exercise => 
        results.value[exercise.exercise_id]?.is_correct
      )
    })

    const loadLesson = async () => {
      try {
        loading.value = true
        window.setLoading(true)
        
        const lessonId = route.params.id
        const data = await contentAPI.getLesson(lessonId)
        lesson.value = data.lesson
        console.log('DEBUG: 课程数据加载完成:', data.lesson.progress)
        
        // Initialize code storage for each exercise
        if (lesson.value.exercises) {
          lesson.value.exercises.forEach(exercise => {
            codes.value[exercise.exercise_id] = `# ${t('lesson.placeholder').replace('...', '')}\n`
            // Initialize feedback modes
            feedbackModes.value[exercise.exercise_id] = 'simple'
            activeFeedbackTabs.value[exercise.exercise_id] = 'structural'
            // Initialize time tracking
            startTimes.value[exercise.exercise_id] = null
            hasStartedCoding.value[exercise.exercise_id] = false
            currentTimes.value[exercise.exercise_id] = 0
            timers.value[exercise.exercise_id] = null
          })
        }
      } catch (error) {
        console.error('Failed to load lesson:', error)
        const message = error.response?.data?.error || t('lesson.notifications.loadingFailed')
        window.showNotification(message, 'error')
      } finally {
        loading.value = false
        window.setLoading(false)
      }
    }

    const onCodeChange = (exerciseId, newCode) => {
      // Update the code
      codes.value[exerciseId] = newCode
      
      // 如果用户修改了代码，并且当前没有在计时，则开始新的计时
      if (!hasStartedCoding.value[exerciseId]) {
        const placeholder = `# ${t('lesson.placeholder').replace('...', '')}\n`
        console.log(`DEBUG: 检查代码变化 - 新代码长度: ${newCode.trim().length}, 占位符: "${placeholder.trim()}", 新代码: "${newCode.trim()}"`)
        
        // 检查是否开始输入实际代码（不只是默认占位符）
        if (newCode.trim().length > 0 && newCode.trim() !== placeholder.trim()) {
          startTimes.value[exerciseId] = new Date()
          hasStartedCoding.value[exerciseId] = true
          startTimer(exerciseId)
          console.log(`✅ 开始做题时间: ${startTimes.value[exerciseId]}`)
        } else {
          console.log(`❌ 代码未变化或仍为占位符，不开始计时`)
        }
      } else {
        console.log(`⏰ 已在计时中，当前计时: ${currentTimes.value[exerciseId]}秒`)
      }
    }

    const startTimer = (exerciseId) => {
      // 清除可能存在的旧计时器
      if (timers.value[exerciseId]) {
        clearInterval(timers.value[exerciseId])
      }
      
      // 启动新计时器，每秒更新一次
      timers.value[exerciseId] = setInterval(() => {
        if (startTimes.value[exerciseId]) {
          const elapsed = Math.floor((Date.now() - startTimes.value[exerciseId].getTime()) / 1000)
          currentTimes.value[exerciseId] = elapsed
        }
      }, 1000)
    }

    const stopTimer = (exerciseId) => {
      if (timers.value[exerciseId]) {
        clearInterval(timers.value[exerciseId])
        timers.value[exerciseId] = null
      }
    }

    const formatTimerTime = (seconds) => {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
    }

    const runCode = async (exercise) => {
      const code = codes.value[exercise.exercise_id]
      if (!code.trim()) {
        window.showNotification(t('lesson.notifications.enterCodeFirst'), 'warning')
        return
      }

      // 点击提交时立即停止计时并计算耗时
      let solvingTimeSec = 0
      const startTime = startTimes.value[exercise.exercise_id]
      if (startTime && hasStartedCoding.value[exercise.exercise_id]) {
        solvingTimeSec = Math.round((Date.now() - startTime.getTime()) / 1000)
        console.log(`本次做题耗时: ${solvingTimeSec}秒`)
        
        // 立即停止计时器
        stopTimer(exercise.exercise_id)
      }

      try {
        codeRunning.value = true
        
        // 准备提交数据
        const submissionData = {
          exercise_id: exercise.exercise_id,
          code: code
        }
        
        // 如果有计时数据，添加到提交中（包括0秒的情况）
        if (startTime && hasStartedCoding.value[exercise.exercise_id]) {
          submissionData.solving_time_sec = solvingTimeSec
          console.log(`传递给后端的做题耗时: ${solvingTimeSec}秒`)
        }
        
        const response = await submissionAPI.submitCode(submissionData)
        
        // Backend returns {submission: {...}, result: {...}}
        const result = response.result
        result.submission = response.submission // Add submission info for analysis display
        results.value[exercise.exercise_id] = result
        
        // 根据结果处理计时状态
        if (result.is_correct) {
          // 如果答案正确，彻底停止计时
          hasStartedCoding.value[exercise.exercise_id] = false
          currentTimes.value[exercise.exercise_id] = 0
          window.showNotification(t('lesson.notifications.codeExecutedCorrectly'), 'success')
        } else {
          // 如果答案错误，重置计时状态，等待用户修改代码时重新开始计时
          hasStartedCoding.value[exercise.exercise_id] = false
          currentTimes.value[exercise.exercise_id] = 0
          startTimes.value[exercise.exercise_id] = null
          window.showNotification(t('lesson.notifications.codeNeedsModification'), 'warning')
        }
        
      } catch (error) {
        console.error('Code execution failed:', error)
        window.showNotification(t('lesson.notifications.executionFailed'), 'error')
        
        // 出错时也重置计时状态
        hasStartedCoding.value[exercise.exercise_id] = false
        currentTimes.value[exercise.exercise_id] = 0
        startTimes.value[exercise.exercise_id] = null
      } finally {
        codeRunning.value = false
      }
    }

    const canShowHints = (exercise) => {
      // Check if there are incorrect results or lesson progress indicates hints should be shown
      const result = results.value[exercise.exercise_id]
      return (result && !result.is_correct) || 
             lesson.value?.progress?.should_show_hint ||
             (result && result.hints_available)
    }

    const requestHint = async (exerciseId) => {
      try {
        hintLoading.value = true
        
        const currentHints = hints.value[exerciseId] || []
        const nextHintLevel = currentHints.length + 1
        
        const data = await submissionAPI.requestHint(exerciseId, nextHintLevel)
        
        if (!hints.value[exerciseId]) {
          hints.value[exerciseId] = []
        }
        hints.value[exerciseId].push(data.hint)
        
        window.showNotification(t('lesson.notifications.hintObtained', { level: data.hint_level, hint: data.hint }), 'info')
      } catch (error) {
        console.error('Failed to get hint:', error)
        const message = error.response?.data?.error || t('lesson.notifications.hintRequestFailed')
        window.showNotification(message, 'error')
      } finally {
        hintLoading.value = false
      }
    }

    const getStatusText = (status) => {
      return t(`lesson.statusLabels.${status}`) || t('lesson.statusLabels.unknown')
    }

    const toggleTheme = () => {
      isDarkTheme.value = !isDarkTheme.value
      // Save theme preference to localStorage
      localStorage.setItem('codeEditorTheme', isDarkTheme.value ? 'dark' : 'light')
    }

    const toggleHistory = async (exerciseId) => {
      if (histories.value[exerciseId]) {
        // Already loaded, toggle display
        histories.value[exerciseId] = null
      } else {
        // First time loading
        try {
          historyLoading.value = true
          const response = await submissionAPI.getSubmissionHistory(exerciseId)
          histories.value[exerciseId] = response.submissions
          window.showNotification(t('lesson.notifications.historyLoaded'), 'success')
        } catch (error) {
          console.error('Failed to get submission history:', error)
          const message = error.response?.data?.error || t('lesson.notifications.historyLoadFailed')
          window.showNotification(message, 'error')
        } finally {
          historyLoading.value = false
        }
      }
    }

    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleString()
    }

    const showSubmissionCode = (submission) => {
      selectedSubmission.value = submission
      showCodeModal.value = true
    }

    const closeCodeModal = () => {
      showCodeModal.value = false
      selectedSubmission.value = null
    }

    const copyToCurrentEditor = () => {
      if (selectedSubmission.value?.code) {
        // Find the exercise that this submission belongs to
        const exerciseId = selectedSubmission.value.exercise_id
        if (exerciseId && codes.value[exerciseId] !== undefined) {
          codes.value[exerciseId] = selectedSubmission.value.code
          window.showNotification(t('lesson.notifications.codeCopiedToEditor'), 'success')
          closeCodeModal()
        }
      }
    }

    // Code quality analysis related methods
    const getScoreClass = (score) => {
      if (score >= 8) return 'excellent'
      if (score >= 6) return 'good'
      if (score >= 4) return 'fair'
      return 'poor'
    }

    const getAnalysisSuggestions = (analysisResult) => {
      if (!analysisResult) return []
      
      try {
        const analysis = typeof analysisResult === 'string' ? JSON.parse(analysisResult) : analysisResult
        const suggestions = []
        
        // 🔧 修复：优先使用新的suggestions_list字段
        if (analysis.summary?.suggestions_list) {
          suggestions.push(...analysis.summary.suggestions_list)
        } else {
          // 回退到原有逻辑
          // Extract suggestions from local analysis
          if (analysis.local_analysis?.quality_issues) {
            suggestions.push(...analysis.local_analysis.quality_issues)
          }
          
          // Extract suggestions from API analysis
          if (analysis.api_analysis) {
            Object.values(analysis.api_analysis).forEach(apiResult => {
              if (apiResult && typeof apiResult === 'object') {
                // 原有的suggestions字段
                if (apiResult.suggestions) {
                  suggestions.push(...apiResult.suggestions)
                }
                
                // 🆕 新增：从AI反馈中提取建议
                if (apiResult.feedback) {
                  const feedback = apiResult.feedback
                  if (typeof feedback === 'object') {
                    // OpenAI风格的反馈
                    if (feedback.learning_suggestions) {
                      suggestions.push(feedback.learning_suggestions)
                    }
                    if (feedback.guiding_hints) {
                      suggestions.push(feedback.guiding_hints)
                    }
                    
                    // DeepSeek风格的反馈
                    if (feedback.hints_not_answers) {
                      suggestions.push(feedback.hints_not_answers)
                    }
                    if (feedback.next_steps) {
                      suggestions.push(feedback.next_steps)
                    }
                  }
                }
              }
            })
          }
        }
        
        // 过滤空值和重复项
        return [...new Set(suggestions.filter(s => s && s.trim()))]
          .slice(0, 5) // 增加显示数量到5个
      } catch (error) {
        console.warn('Failed to parse analysis result:', error)
        return ['分析结果解析失败，请查看详细反馈信息']
      }
    }

    // 🔥 NEW: Feedback comparison related methods
    const deepseekLoading = ref({});

    const toggleFeedbackMode = async (exerciseId) => {
      const currentMode = feedbackModes.value[exerciseId];
      const newMode = currentMode === 'simple' ? 'detailed' : 'simple';
      feedbackModes.value[exerciseId] = newMode;

      // If switching to detailed mode and DeepSeek data hasn't loaded yet, trigger loading
      if (newMode === 'detailed' && !isDeepSeekDataAvailable(exerciseId)) {
        deepseekLoading.value[exerciseId] = true;
        try {
          const submissionId = results.value[exerciseId].submission.submission_id;
          const response = await submissionAPI.requestAdditionalFeedback(submissionId, 'deepseek');
          
          // Merge new feedback data into existing results
          const newFeedbackData = response.feedback_content;
          const comprehensiveFeedback = results.value[exerciseId].comprehensive_feedback.comprehensive_feedback || results.value[exerciseId].comprehensive_feedback;
          
          comprehensiveFeedback.ai_generated.deepseek = {
              feedback: JSON.parse(newFeedbackData.feedback_content),
              generation_time: newFeedbackData.generation_time_ms,
              cost: newFeedbackData.api_cost,
              source: 'deepseek_educational',
              feedback_id: newFeedbackData.feedback_id
          };

          // Update UI
          window.showNotification(t('lesson.notifications.deepseekFeedbackLoaded'), 'success');
        } catch (error) {
          console.error('Failed to load DeepSeek feedback:', error);
          window.showNotification(t('lesson.notifications.deepseekFeedbackFailed'), 'error');
        } finally {
          deepseekLoading.value[exerciseId] = false;
        }
      }

      // Set default active tab
      if (newMode === 'detailed') {
        const result = results.value[exerciseId];
        if (result?.comprehensive_feedback?.recommended_primary) {
          activeFeedbackTabs.value[exerciseId] = result.comprehensive_feedback.recommended_primary;
        }
      }
    };

    const isDeepSeekDataAvailable = (exerciseId) => {
        const result = results.value[exerciseId];
        const feedbacks = result?.comprehensive_feedback?.comprehensive_feedback?.ai_generated || {};
        return feedbacks.deepseek && !feedbacks.deepseek.error;
    };

    const getAvailableFeedbacks = (exerciseId) => {
      const result = results.value[exerciseId];
      if (!result?.comprehensive_feedback) return {};
      
      const feedbacks = {};
      const comprehensiveFeedback = result.comprehensive_feedback.comprehensive_feedback || result.comprehensive_feedback;
      
      // Add structural feedback
      feedbacks['structural'] = {
        feedback: comprehensiveFeedback.structural,
        generation_time: 0,
        cost: 0
      };
      
      // Add existing AI feedback
      const aiFeedbacks = comprehensiveFeedback.ai_generated || {};
      Object.keys(aiFeedbacks).forEach(apiName => {
        feedbacks[apiName] = aiFeedbacks[apiName];
      });

      // If DeepSeek should be shown but data is not yet available, add a placeholder
      if (feedbackModes.value[exerciseId] === 'detailed' && !isDeepSeekDataAvailable(exerciseId)) {
          feedbacks['deepseek'] = { placeholder: true, loading: deepseekLoading.value[exerciseId] };
      }
      
      return feedbacks;
    };

    const getFeedbackTypeLabel = (feedbackType) => {
      return t(`lesson.feedbackTypeLabels.${feedbackType}`) || feedbackType
    }

    const getFeedbackComponent = (feedbackType) => {
      // Return different component names based on feedback type
      if (feedbackType === 'structural') {
        return 'StructuralFeedback'
      } else if (feedbackType.includes('openai')) {
        return 'AIFeedback'
      } else if (feedbackType.includes('deepseek')) {
        return 'AIFeedback'
      }
      return 'BasicFeedback'
    }

    const getActiveFeedbackData = (exerciseId) => {
      const feedbacks = getAvailableFeedbacks(exerciseId)
      const activeType = activeFeedbackTabs.value[exerciseId]
      return feedbacks[activeType] || {}
    }

    const getStageLabel = (stage) => {
      return t(`lesson.stageLabels.${stage}`) || t('lesson.stageLabels.unknown')
    }

    const feedbackSourceInfo = computed(() => (exerciseId) => {
      const feedbacks = getAvailableFeedbacks(exerciseId);
      const sources = Object.keys(feedbacks).filter(key => !feedbacks[key].placeholder);
      const sourceNames = sources.map(s => {
          if (s === 'structural') return 'Judge0';
          if (s.includes('openai')) return 'OpenAI';
          if (s.includes('deepseek')) return 'DeepSeek';
          return 'Other';
      });
      return {
          count: sources.length,
          names: `(${sourceNames.join(', ')})`
      };
    });

    const handleFeedbackRating = async (data) => {
      try {
        // Submit feedback rating
        await submissionAPI.rateFeedback(data.feedback_id, data.helpfulness_rating, data.clarity_rating)
        window.showNotification(t('lesson.notifications.feedbackRatingSuccess'), 'success')
      } catch (error) {
        console.error('Failed to submit rating:', error)
        window.showNotification(t('lesson.notifications.feedbackRatingFailed'), 'error')
      }
    }

    // 过滤可见的测试用例 (不显示隐藏的测试用例)
    const getVisibleTestCases = (exercise) => {
      if (!exercise?.test_cases) return []
      return exercise.test_cases.filter(testCase => !testCase.is_hidden)
    }

    // Restore theme preference from localStorage
    const savedTheme = localStorage.getItem('codeEditorTheme')
    if (savedTheme) {
      isDarkTheme.value = savedTheme === 'dark'
    }

    onMounted(() => {
      loadLesson()
    })

    // 组件卸载时清理所有计时器
    onUnmounted(() => {
      Object.keys(timers.value).forEach(exerciseId => {
        if (timers.value[exerciseId]) {
          clearInterval(timers.value[exerciseId])
        }
      })
    })

    return {
      t,
      lesson,
      loading,
      codeRunning,
      hintLoading,
      isDarkTheme,
      codes,
      results,
      hints,
      // NEW: Submission history functionality
      histories,
      historyLoading,
      toggleHistory,
      formatTime,
      // NEW: Code modal functionality
      showCodeModal,
      selectedSubmission,
      showSubmissionCode,
      closeCodeModal,
      copyToCurrentEditor,
      renderedContent,
      allExercisesCompleted,
      loadLesson,
      onCodeChange,
      runCode,
      canShowHints,
      requestHint,
      getStatusText,
      toggleTheme,
      getScoreClass,
      getAnalysisSuggestions,
      // 🔥 NEW: Feedback comparison functionality
      feedbackModes,
      activeFeedbackTabs,
      toggleFeedbackMode,
      getAvailableFeedbacks,
      getFeedbackTypeLabel,
      getFeedbackComponent,
      getActiveFeedbackData,
      getStageLabel,
      handleFeedbackRating,
      deepseekLoading,
      feedbackSourceInfo,
      // 🔥 NEW: Time tracking functionality
      startTimes,
      hasStartedCoding,
      currentTimes,
      formatTimerTime,
      // 🔥 NEW: Test case visibility filtering
      getVisibleTestCases
    }
  }
}
</script>

<style scoped>
.lesson-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 1rem 2rem 1rem;
  background-color: var(--color-slate-50);
  min-height: 100vh;
}

/* 通用卡片样式 */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 导航样式 */
.lesson-nav {
  padding: 1.5rem;
}

.nav-content {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
}

.nav-left {
  flex-shrink: 0;
}

.lesson-title {
  flex: 1;
}

.lesson-title h1 {
  color: var(--color-slate-900);
  margin: 0;
  font-size: 1.875rem;
  font-weight: 700;
}

.lesson-progress {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.progress-badge {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
}

.progress-badge.not_started {
  background: var(--color-slate-50);
  color: var(--color-slate-600);
  border: 1px solid var(--color-slate-200);
}

.progress-badge.in_progress {
  background: var(--color-amber-50);
  color: var(--color-amber-800);
  border: 1px solid var(--color-amber-300);
}

.progress-badge.completed {
  background: var(--color-green-100);
  color: var(--color-green-800);
  border: 1px solid var(--color-green-300);
}

.attempts-text {
  font-size: 0.875rem;
  color: var(--color-slate-500);
}

.avg-time-text {
  font-size: 0.875rem;
  color: var(--color-blue-600);
  font-weight: 500;
  background: var(--color-blue-50);
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  border: 1px solid var(--color-blue-200);
}

/* 按钮样式 */
.btn {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
  font-size: 0.875rem;
}

.btn-secondary {
  background-color: var(--color-slate-200);
  color: var(--color-slate-600);
}

.btn-secondary:hover {
  background-color: var(--color-slate-300);
}

.btn-success {
  background-color: var(--color-green-500);
  color: white;
}

.btn-success:hover {
  background-color: var(--color-green-600);
}

.lesson-content {
  display: grid;
  gap: 30px;
}

.content-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid var(--color-slate-200);
}

.content-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 采用简洁现代的设计风格 - 使用:deep()确保样式应用到v-html内容 */
.markdown-content {
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.7;
  color: var(--color-slate-600);
  font-size: 1rem;
}

/* 标题样式 - 简洁风格 */
.markdown-content :deep(h1) {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--color-slate-900);
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--color-blue-600);
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.markdown-content :deep(h1:first-child) {
  margin-top: 0;
}

.markdown-content :deep(h2) {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-slate-700);
  margin: 2rem 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--color-blue-600);
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.markdown-content :deep(h3) {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-slate-700);
  margin: 1.5rem 0 0.75rem 0;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.markdown-content :deep(h4) {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-slate-700);
  margin: 1.25rem 0 0.75rem 0;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 段落样式 */
.markdown-content :deep(p) {
  margin-bottom: 1.5rem;
  line-height: 1.7;
  color: var(--color-slate-600);
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 代码块样式 - 采用深色主题 */
.markdown-content :deep(pre) {
  background-color: var(--color-slate-800);
  color: var(--color-slate-400);
  font-family: 'Fira Code', 'Consolas', monospace;
  padding: 1.5rem;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre;
  overflow-x: auto;
  margin: 1.5rem 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.markdown-content :deep(pre code) {
  background: none !important;
  color: inherit;
  padding: 0;
  font-size: inherit;
  border-radius: 0;
}

/* 行内代码样式 */
.markdown-content :deep(code) {
  background-color: var(--color-slate-100);
  color: var(--color-red-600);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Fira Code', 'Consolas', monospace;
  font-size: 0.875rem;
  font-weight: 500;
}

/* 列表样式 - 简洁风格 */
.markdown-content :deep(ul), .markdown-content :deep(ol) {
  margin: 1.5rem 0;
  padding-left: 1.5rem;
}

.markdown-content :deep(ul li), .markdown-content :deep(ol li) {
  margin-bottom: 0.5rem;
  color: var(--color-slate-600);
  line-height: 1.6;
}

.markdown-content :deep(ul) {
  list-style-type: disc;
}

.markdown-content :deep(ol) {
  list-style-type: decimal;
}

/* 引用块样式 */
.markdown-content :deep(blockquote) {
  background-color: var(--color-slate-50);
  border-left: 4px solid var(--color-blue-600);
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  border-radius: 8px;
  color: var(--color-slate-600);
  font-style: italic;
}

.markdown-content :deep(blockquote p) {
  margin: 0;
}

/* 表格样式 */
.markdown-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  overflow: hidden;
}

.markdown-content :deep(th) {
  background-color: var(--color-slate-50);
  color: var(--color-slate-700);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  border-bottom: 1px solid var(--color-slate-200);
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.markdown-content :deep(td) {
  padding: 1rem;
  border-bottom: 1px solid var(--color-slate-200);
  background-color: white;
  color: var(--color-slate-600);
}

.markdown-content :deep(tr:nth-child(even) td) {
  background-color: var(--color-slate-50);
}

.markdown-content :deep(tr:hover td) {
  background-color: var(--color-blue-100);
}

/* 强调文本 */
.markdown-content :deep(strong) {
  color: var(--color-slate-700);
  font-weight: 700;
}

.markdown-content :deep(em) {
  color: var(--color-slate-600);
  font-style: italic;
}

/* 链接样式 */
.markdown-content :deep(a) {
  color: var(--color-blue-600);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.markdown-content :deep(a:hover) {
  color: var(--color-blue-700);
  text-decoration: underline;
}

/* 分割线 */
.markdown-content :deep(hr) {
  border: none;
  height: 1px;
  background-color: var(--color-slate-200);
  margin: 2rem 0;
}

/* 图片样式 */
.markdown-content :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 1.5rem 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lesson-container {
    padding: 0.5rem;
  }
  
  .lesson-nav {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .lesson-title h1 {
    font-size: 1.5rem;
  }
  
  .content-section {
    padding: 1.5rem;
  }
  
  .markdown-content {
    font-size: 0.9rem;
  }
  
  .markdown-content h1 {
    font-size: 1.5rem;
  }
  
  .markdown-content h2 {
    font-size: 1.25rem;
  }
  
  .markdown-content h3 {
    font-size: 1.125rem;
  }
}

.exercises-section h2 {
  color: var(--color-slate-800);
  margin-bottom: 20px;
}

.exercise-card {
  background: white;
  border-radius: 10px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.exercise-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 20px;
}

.exercise-header h3 {
  color: var(--color-slate-800);
  margin: 0;
}

.problem-statement {
  background: var(--color-slate-50);
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid var(--color-blue-600);
  margin-bottom: 20px;
}

.hints-section {
  background: var(--color-amber-50);
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid var(--color-amber-500);
  margin-bottom: 20px;
}

.hints-section h4 {
  color: var(--color-amber-800);
  margin-bottom: 12px;
}

.hint-item {
  margin-bottom: 8px;
  color: var(--color-amber-800);
}

/* Submission History Styles */
.history-section {
  background: var(--color-blue-50);
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid var(--color-blue-600);
  margin-bottom: 20px;
}

.history-section h4 {
  color: var(--color-blue-800);
  margin-bottom: 12px;
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  background: white;
  padding: 10px 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  border-left: 3px solid var(--color-slate-500);
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.history-item.clickable {
  cursor: pointer;
}

.history-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transform: translateY(-1px);
}

.history-item.clickable:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transform: translateY(-2px);
  background: linear-gradient(to right, var(--color-slate-50), white);
}

.history-item.success {
  border-left-color: var(--color-green-600);
  background: linear-gradient(to right, var(--color-green-50), white);
}

.history-time {
  color: var(--color-slate-500);
  font-size: 12px;
  min-width: 120px;
}

.history-status {
  font-size: 16px;
  min-width: 24px;
}

.history-attempts {
  color: var(--color-slate-600);
  font-weight: 500;
  min-width: 80px;
}

.history-error {
  color: var(--color-red-500);
  font-size: 12px;
  font-style: italic;
  opacity: 0.8;
}

.exercise-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.history-hint {
  margin-left: auto;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.history-item.clickable:hover .history-hint {
  opacity: 1;
}

/* Code Modal Styles */
.code-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.code-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.code-modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--color-slate-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--color-slate-50);
}

.code-modal-header h3 {
  margin: 0;
  color: var(--color-slate-800);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--color-slate-500);
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: var(--color-slate-200);
  color: var(--color-slate-600);
}

.code-modal-info {
  padding: 16px 24px;
  background: var(--color-slate-50);
  border-bottom: 1px solid var(--color-slate-200);
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.modal-time {
  color: var(--color-slate-500);
  font-size: 14px;
}

.modal-status {
  font-weight: 500;
}

.modal-error {
  color: var(--color-red-500);
  font-size: 14px;
  font-style: italic;
}

.code-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  min-height: 200px;
  max-height: 400px;
}

.code-modal-content pre {
  background: var(--color-slate-50);
  border: 1px solid var(--color-slate-200);
  border-radius: 8px;
  padding: 16px;
  margin: 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: var(--color-slate-800);
}

.code-modal-content code {
  background: none;
  padding: 0;
  border-radius: 0;
  font-family: inherit;
  color: inherit;
}

.code-modal-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--color-slate-200);
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  background: var(--color-slate-50);
}

/* Responsive design for modal */
@media (max-width: 768px) {
  .code-modal {
    width: 95vw;
    max-height: 85vh;
  }
  
  .code-modal-header,
  .code-modal-info,
  .code-modal-content,
  .code-modal-footer {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .code-modal-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

.code-editor-section {
  margin-bottom: 20px;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--color-slate-800);
  color: white;
  padding: 12px 16px;
  border-radius: 8px 8px 0 0;
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.coding-timer {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 255, 255, 0.15);
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  color: var(--color-slate-100);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: pulse 2s infinite;
  backdrop-filter: blur(4px);
}

.timer-icon {
  font-size: 16px;
}

.timer-text {
  font-weight: 600;
  font-family: 'Courier New', monospace;
  letter-spacing: 0.5px;
}

@keyframes pulse {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.8; 
    transform: scale(1.02);
  }
}

.editor-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.code-editor {
  border-radius: 0 0 8px 8px;
  overflow: hidden;
}

.result-section {
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.result-header {
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
}

.result-header.success {
  background: var(--color-green-50);
  color: var(--color-green-800);
}

.result-header.error {
  background: var(--color-red-50);
  color: var(--color-red-800);
}

.result-icon {
  font-size: 20px;
}

.feedback-message {
  padding: 16px;
  background: white;
  border-left: 4px solid var(--color-slate-300);
}

.error-details {
  background: var(--color-slate-50);
  padding: 16px;
}

.error-message, .output-section, .expected-output {
  margin-bottom: 16px;
}

.error-details pre {
  background: white;
  padding: 12px;
  border-radius: 4px;
  margin-top: 8px;
  white-space: pre-wrap;
  font-size: 14px;
}

.hint-reminder {
  background: var(--color-blue-50);
  color: var(--color-blue-700);
  padding: 12px 16px;
  border-radius: 6px;
  margin-top: 12px;
}

/* Code quality analysis styles */
.code-quality-section {
  background: var(--color-slate-50);
  border: 1px solid var(--color-slate-200);
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.code-quality-section h4 {
  margin: 0 0 12px 0;
  color: var(--color-slate-800);
}

.quality-metrics {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.metric {
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-label {
  font-weight: 500;
  color: var(--color-slate-600);
}

.metric-value {
  font-weight: 600;
  color: var(--color-slate-800);
}

.quality-score.excellent {
  color: var(--color-green-700);
}

.quality-score.good {
  color: var(--color-green-600);
}

.quality-score.fair {
  color: var(--color-amber-600);
}

.quality-score.poor {
  color: var(--color-red-600);
}

/* 🔧 修复：独立的改进建议板块样式 */
.improvement-suggestions-section {
  background: var(--color-slate-50);
  border: 1px solid var(--color-slate-200);
  padding: 16px;
  border-radius: 8px;
  margin-top: 16px;
}

.improvement-suggestions-section h4 {
  margin: 0 0 16px 0;
  color: var(--color-slate-800);
  display: flex;
  align-items: center;
  font-size: 18px;
}

/* 移除标题前的💡，因为翻译文本中已包含 */

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.suggestion-item {
  background: white;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid var(--color-blue-600);
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.suggestion-text {
  color: var(--color-slate-600);
  font-size: 14px;
  line-height: 1.5;
  display: block;
  width: 100%;
}

.suggestion-icon {
  font-size: 16px;
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
}

.no-suggestions {
  display: flex;
  align-items: center;
  background: white;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid var(--color-slate-500);
  font-style: italic;
}

.test-cases-section {
  background: var(--color-slate-50);
  padding: 16px;
  border-radius: 8px;
  margin-top: 20px;
}

.test-cases-section h4 {
  color: var(--color-slate-800);
  margin-bottom: 12px;
}

.test-case {
  background: white;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  border-left: 3px solid var(--color-blue-600);
}

.completion-banner {
  background: linear-gradient(135deg, var(--color-blue-500) 0%, var(--color-blue-600) 100%);
  color: white;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  margin-top: 30px;
}

.completion-content h3 {
  margin-bottom: 16px;
}

.completion-content p {
  margin-bottom: 24px;
  opacity: 0.9;
}

.completion-content .btn {
  background: white;
  color: var(--color-blue-500);
}

.loading-container, .error-container {
  text-align: center;
  padding: 60px 20px;
}

.loading-container .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-slate-200);
  border-top: 4px solid var(--color-blue-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 🔥 NEW: Feedback comparison functionality styles */
.feedback-controls {
  margin-left: auto;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.comprehensive-feedback-section {
  background: var(--color-slate-50);
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.feedback-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.feedback-tab {
  padding: 8px 16px;
  border: 1px solid var(--color-slate-300);
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.feedback-tab:hover {
  background: var(--color-slate-200);
  border-color: var(--color-slate-400);
}

.feedback-tab.active {
  background: var(--color-blue-600);
  color: white;
  border-color: var(--color-blue-600);
}

.feedback-tab.disabled {
  cursor: not-allowed;
  background: var(--color-slate-50);
  color: var(--color-slate-500);
}

.spinner-sm {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 0.6s linear infinite;
  margin-right: 5px;
}

.generation-time {
  font-size: 12px;
  opacity: 0.8;
  font-weight: normal;
}

.feedback-content-area {
  display: grid;
  gap: 16px;
}

.active-feedback-content {
  background: white;
  border-radius: 6px;
  padding: 16px;
  border-left: 4px solid var(--color-blue-600);
}

.feedback-insights {
  background: var(--color-blue-50);
  border-radius: 6px;
  padding: 16px;
  border-left: 4px solid #17a2b8;
}

.feedback-insights h5 {
  margin: 0 0 12px 0;
  color: #0c5460;
}

.insights-content p {
  margin: 8px 0;
  font-size: 14px;
  color: #0c5460;
}

.insights-content strong {
  color: var(--color-slate-600);
}

.source-names {
    font-size: 13px;
    color: var(--color-slate-500);
    margin-left: 4px;
}

/* Responsive design */
@media (max-width: 768px) {
  .feedback-tabs {
    flex-direction: column;
  }
  
  .feedback-tab {
    width: 100%;
    justify-content: center;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .feedback-controls {
    margin-left: 0;
    width: 100%;
  }
}
</style>