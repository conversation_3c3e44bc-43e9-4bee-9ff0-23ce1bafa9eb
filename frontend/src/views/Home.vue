<template>
  <div class="home-container">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-content">
        <div class="logo">
          <h1>{{ $t('home.nav.brand') }}</h1>
        </div>
        <div class="nav-links">
          <template v-if="!isLoggedIn">
            <router-link to="/login" class="nav-link">{{ $t('home.nav.studentLogin') }}</router-link>
            <router-link to="/register" class="nav-link">{{ $t('home.nav.register') }}</router-link>
          </template>
          <template v-else>
            <router-link v-if="isAdmin" to="/admin/dashboard" class="nav-link admin-link">{{ $t('home.nav.adminDashboard') }}</router-link>
            <router-link v-else to="/dashboard" class="nav-link">{{ $t('home.nav.dashboard') }}</router-link>
          </template>
          <router-link v-if="!isLoggedIn" to="/admin/login" class="nav-link admin-link">{{ $t('home.nav.admin') }}</router-link>
          <LanguageSwitcher />
        </div>
      </div>
    </nav>

    <!-- 英雄区域 -->
    <!-- 英雄区域 -->
    <section class="hero">
      <div class="hero-container">
        <div class="hero-content">
          <h1 class="hero-title">
            {{ $t('home.hero.title') }} <span class="highlight">{{ $t('home.hero.titleHighlight') }}</span> {{ $t('home.hero.titleEnd') }}
          </h1>
          <p class="hero-subtitle">
            {{ $t('home.hero.subtitle') }}<br>
            {{ $t('home.hero.subtitleLine2') }}
          </p>
          <div class="hero-buttons">
            <template v-if="!isLoggedIn">
              <router-link to="/register" class="btn btn-primary">
                {{ $t('home.hero.startLearning') }}
              </router-link>
              <router-link to="/login" class="btn btn-secondary">
                {{ $t('home.hero.hasAccount') }}
              </router-link>
            </template>
            <template v-else>
              <router-link v-if="isAdmin" to="/admin/dashboard" class="btn btn-primary">
                {{ $t('home.hero.goToAdminDashboard') }}
              </router-link>
              <router-link v-else to="/dashboard" class="btn btn-primary">
                {{ $t('home.hero.continueLearning') }}
              </router-link>
            </template>
          </div>
        </div>
        <div class="hero-image">
          <div class="code-preview">
            <div class="code-header">
              <span class="dot red"></span>
              <span class="dot yellow"></span>
              <span class="dot green"></span>
            </div>
            <div class="code-content">
              <pre><code>{{ $t('home.codePreview.comment1') }}
print("Hello, World!")

{{ $t('home.codePreview.comment2') }}
name = "{{ $t('home.codePreview.learnerName') }}"
age = 25
print(f"{{ $t('home.codePreview.ageOutput', { name: '{name}', age: '{age}' }) }}")

{{ $t('home.codePreview.comment3') }}
if age >= 18:
    print("{{ $t('home.codePreview.adultMessage') }}")
else:
    print("{{ $t('home.codePreview.childMessage') }}")
</code></pre>
            </div>
          </div>
        </div>
      </div>
    </section>    <!-- 功能特色 -->
    <section class="features">
      <div class="container">
        <h2 class="section-title">{{ $t('home.features.title') }}</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon zero-setup-icon"></div>
            <h3>{{ $t('home.features.zeroSetup.title') }}</h3>
            <p>{{ $t('home.features.zeroSetup.description') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon instant-feedback-icon"></div>
            <h3>{{ $t('home.features.instantFeedback.title') }}</h3>
            <p>{{ $t('home.features.instantFeedback.description') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon personalized-learning-icon"></div>
            <h3>{{ $t('home.features.personalizedLearning.title') }}</h3>
            <p>{{ $t('home.features.personalizedLearning.description') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon progressive-teaching-icon"></div>
            <h3>{{ $t('home.features.progressiveTeaching.title') }}</h3>
            <p>{{ $t('home.features.progressiveTeaching.description') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon smart-hints-icon"></div>
            <h3>{{ $t('home.features.smartHints.title') }}</h3>
            <p>{{ $t('home.features.smartHints.description') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon learning-tracking-icon"></div>
            <h3>{{ $t('home.features.learningTracking.title') }}</h3>
            <p>{{ $t('home.features.learningTracking.description') }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 学习路径 -->
    <section class="learning-path">
      <div class="container">
        <h2 class="section-title">{{ $t('home.learningPath.title') }}</h2>
        <div class="path-steps">
          <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h3>{{ $t('home.learningPath.step1.title') }}</h3>
              <p>{{ $t('home.learningPath.step1.description') }}</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h3>{{ $t('home.learningPath.step2.title') }}</h3>
              <p>{{ $t('home.learningPath.step2.description') }}</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h3>{{ $t('home.learningPath.step3.title') }}</h3>
              <p>{{ $t('home.learningPath.step3.description') }}</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">4</div>
            <div class="step-content">
              <h3>{{ $t('home.learningPath.step4.title') }}</h3>
              <p>{{ $t('home.learningPath.step4.description') }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 行动号召 -->
    <section class="cta" v-if="!isLoggedIn">
      <div class="container">
        <h2>{{ $t('home.cta.title') }}</h2>
        <p>{{ $t('home.cta.subtitle') }}</p>
        <router-link to="/register" class="btn btn-primary btn-large">
          {{ $t('home.cta.registerNow') }}
        </router-link>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>{{ $t('home.footer.brand') }}</h3>
            <p>{{ $t('home.footer.brandSubtitle') }}</p>
          </div>
          <div class="footer-section">
            <h4>{{ $t('home.footer.quickLinks') }}</h4>
            <ul>
              <li v-if="!isLoggedIn">
                <router-link to="/login">{{ $t('home.footer.studentLogin') }}</router-link>
              </li>
              <li v-if="!isLoggedIn">
                <router-link to="/register">{{ $t('home.footer.registerAccount') }}</router-link>
              </li>
              <li v-if="isLoggedIn && !isAdmin">
                <router-link to="/dashboard">{{ $t('home.footer.dashboard') }}</router-link>
              </li>
              <li v-if="isLoggedIn && isAdmin">
                <router-link to="/admin/dashboard">{{ $t('home.footer.adminDashboard') }}</router-link>
              </li>
              <li v-if="!isLoggedIn">
                <router-link to="/admin/login">{{ $t('home.footer.adminLogin') }}</router-link>
              </li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>{{ $t('home.footer.learningSupport') }}</h4>
            <ul>
              <li><a href="#">{{ $t('home.footer.userGuide') }}</a></li>
              <li><a href="#">{{ $t('home.footer.faq') }}</a></li>
              <li><a href="#">{{ $t('home.footer.contactUs') }}</a></li>
            </ul>
          </div>
        </div>
        <div class="footer-bottom">
          <p>{{ $t('home.footer.copyright') }}</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import LanguageSwitcher from '../components/LanguageSwitcher.vue'

const { t } = useI18n()

// 检查用户登录状态
const isLoggedIn = computed(() => {
  return !!localStorage.getItem('authToken')
})

const isAdmin = computed(() => {
  const userData = localStorage.getItem('user')
  if (!userData) return false
  try {
    const user = JSON.parse(userData)
    return user.role === 'admin'
  } catch {
    return false
  }
})
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: var(--color-slate-50);
}

/* 导航栏 */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid var(--color-slate-200);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
}

.logo h1 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-slate-900);
  letter-spacing: -0.025em;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.nav-link {
  text-decoration: none;
  color: var(--color-slate-600);
  font-weight: 500;
  font-size: 0.875rem;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.4s ease;
}

.nav-link:hover::before {
  left: 100%;
}

.nav-link:hover {
  color: var(--color-slate-900);
  background-color: var(--color-slate-100);
  transform: translateY(-1px);
}

.admin-link {
  color: var(--color-amber-600) !important;
  border: 1px solid var(--color-amber-200);
}

.admin-link:hover {
  background-color: var(--color-amber-50) !important;
  border-color: var(--color-amber-300);
  color: var(--color-amber-700) !important;
}

/* 英雄区域 */
.hero {
  padding: var(--spacing-12) 0;
  background: linear-gradient(135deg, var(--color-slate-50) 0%, var(--color-blue-50) 100%);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
  animation: heroBackground 20s ease-in-out infinite;
}

@keyframes heroBackground {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-12) var(--spacing-6);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-12);
  align-items: center;
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--color-slate-200);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
}

.hero-content {
  color: var(--color-slate-900);
}

.hero-title {
  font-size: 3rem;
  margin-bottom: var(--spacing-5);
  line-height: 1.2;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.highlight {
  background: linear-gradient(135deg, var(--color-blue-600), var(--color-purple-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  font-weight: 800;
}

.hero-subtitle {
  font-size: 1.125rem;
  margin-bottom: var(--spacing-8);
  color: var(--color-slate-600);
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-4);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3) var(--spacing-6);
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: white;
  color: var(--color-blue-600);
  border-color: var(--color-blue-500);
}

.btn-primary:hover {
  background: var(--color-blue-50);
  border-color: var(--color-blue-600);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
}

.btn-secondary {
  background: white;
  color: var(--color-slate-600);
  border-color: var(--color-slate-300);
}

.btn-secondary:hover {
  background: var(--color-slate-50);
  border-color: var(--color-slate-500);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.25);
}

.btn-large {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: 1rem;
  font-weight: 600;
}

/* 代码预览 */
.code-preview {
  background: #1e1e1e;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.code-preview:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.3);
}

.code-header {
  background: linear-gradient(135deg, #2d2d2d, #3a3a3a);
  padding: 16px 20px;
  display: flex;
  gap: 10px;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.dot:hover {
  transform: scale(1.2);
}

.dot.red { 
  background: #ff5f57; 
  box-shadow: 0 0 8px rgba(255, 95, 87, 0.5);
}
.dot.yellow { 
  background: #ffbd2e; 
  box-shadow: 0 0 8px rgba(255, 189, 46, 0.5);
}
.dot.green { 
  background: #28ca42; 
  box-shadow: 0 0 8px rgba(40, 202, 66, 0.5);
}

.code-content {
  padding: 24px;
  background: linear-gradient(135deg, #1e1e1e, #252525);
}

.code-content pre {
  margin: 0;
  color: #e6e6e6;
  font-family: 'Fira Code', 'JetBrains Mono', 'Cascadia Code', monospace;
  line-height: 1.7;
  font-size: 14px;
}

/* 功能特色 */
.features {
  padding: var(--spacing-12) var(--spacing-6);
  background: var(--color-slate-50);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: var(--spacing-12);
  color: var(--color-slate-900);
  font-weight: 700;
  letter-spacing: -0.025em;
  position: relative;
  padding-bottom: var(--spacing-4);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(135deg, var(--color-blue-500), var(--color-purple-500));
  border-radius: 2px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-6);
}

.feature-card {
  background: white;
  padding: var(--spacing-8);
  border-radius: var(--radius-xl);
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid var(--color-slate-200);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  transition: left 0.6s ease;
}

.feature-card:hover::before {
  left: 100%;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
  border-color: var(--color-slate-300);
}

.feature-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto var(--spacing-5);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
}

.feature-icon::before {
  content: '';
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 8px;
}

/* 零配置学习图标 */
.zero-setup-icon {
  background: linear-gradient(135deg, var(--color-blue-100), var(--color-blue-200));
}

.zero-setup-icon::before {
  background: var(--color-blue-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

/* 即时反馈图标 */
.instant-feedback-icon {
  background: linear-gradient(135deg, var(--color-amber-100), var(--color-amber-200));
}

.instant-feedback-icon::before {
  background: var(--color-amber-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M13 10V3L4 14h7v7l9-11h-7z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

/* 个性化学习图标 */
.personalized-learning-icon {
  background: linear-gradient(135deg, var(--color-green-100), var(--color-green-200));
}

.personalized-learning-icon::before {
  background: var(--color-green-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

/* 渐进式教学图标 */
.progressive-teaching-icon {
  background: linear-gradient(135deg, var(--color-purple-100), var(--color-purple-200));
}

.progressive-teaching-icon::before {
  background: var(--color-purple-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

/* 智能提示图标 */
.smart-hints-icon {
  background: linear-gradient(135deg, var(--color-indigo-100), var(--color-indigo-200));
}

.smart-hints-icon::before {
  background: var(--color-indigo-600);
  mask: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 2C8.13 2 5 5.13 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.87-3.13-7-7-7zM9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1z' fill='currentColor'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
  -webkit-mask: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 2C8.13 2 5 5.13 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.87-3.13-7-7-7zM9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1z' fill='currentColor'/%3E%3C/svg%3E") no-repeat center;
  -webkit-mask-size: contain;
}

/* 学习跟踪图标 */
.learning-tracking-icon {
  background: linear-gradient(135deg, var(--color-red-100), var(--color-red-200));
}

.learning-tracking-icon::before {
  background: var(--color-red-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.feature-card h3 {
  color: var(--color-slate-900);
  margin-bottom: var(--spacing-3);
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.feature-card p {
  color: var(--color-slate-600);
  line-height: 1.6;
  font-size: 0.875rem;
}

/* 学习路径 */
.learning-path {
  padding: var(--spacing-12) 0;
  background: white;
}

.learning-path .container {
  padding: 0 var(--spacing-6);
}

.path-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-6);
}

.step {
  background: var(--color-slate-50);
  padding: var(--spacing-8);
  border-radius: var(--radius-xl);
  text-align: center;
  border: 1px solid var(--color-slate-200);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.step::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.7), transparent);
  transition: left 0.6s ease;
}

.step:hover::before {
  left: 100%;
}

.step:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
  border-color: var(--color-slate-300);
  background: white;
}

.step-number {
  background: linear-gradient(135deg, var(--color-blue-500), var(--color-blue-600));
  color: white;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.25rem;
  margin: 0 auto var(--spacing-5);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.step:hover .step-number {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.step-content h3 {
  color: var(--color-slate-900);
  margin-bottom: var(--spacing-3);
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.step-content p {
  color: var(--color-slate-600);
  line-height: 1.6;
  font-size: 0.875rem;
}

/* 行动号召 */
.cta {
  padding: var(--spacing-12) 0;
  background: linear-gradient(135deg, var(--color-slate-50) 0%, var(--color-blue-50) 100%);
  position: relative;
  overflow: hidden;
}

.cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 30% 40%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 60%, rgba(147, 51, 234, 0.1) 0%, transparent 50%);
  animation: ctaBackground 15s ease-in-out infinite;
}

@keyframes ctaBackground {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.cta .container {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--color-slate-200);
  padding: var(--spacing-12) var(--spacing-6);
  text-align: center;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
}

.cta h2 {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-5);
  color: var(--color-slate-900);
  font-weight: 700;
  letter-spacing: -0.025em;
}

.cta p {
  font-size: 1.125rem;
  margin-bottom: var(--spacing-8);
  color: var(--color-slate-600);
  line-height: 1.6;
}

/* 页脚 */
.footer {
  background: linear-gradient(135deg, var(--color-slate-800), var(--color-slate-900));
  color: white;
  padding: 48px 20px 24px;
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(147, 51, 234, 0.1) 0%, transparent 50%);
  animation: footerBackground 25s ease-in-out infinite;
}

@keyframes footerBackground {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 16px;
  color: white;
  font-weight: 600;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section ul li {
  margin-bottom: 10px;
}

.footer-section a {
  color: var(--color-slate-300);
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 4px 0;
  border-radius: 4px;
  display: inline-block;
}

.footer-section a:hover {
  color: white;
  transform: translateX(4px);
}

.footer-bottom {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid var(--color-slate-700);
  color: var(--color-slate-400);
  position: relative;
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero::before,
  .cta::before,
  .footer::before {
    animation-duration: 30s;
  }
  
  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
    text-align: center;
    padding: var(--spacing-8) var(--spacing-4);
    margin: 0 var(--spacing-4);
  }
  
  .hero-title {
    font-size: 2.25rem;
  }
  
  .hero-buttons {
    justify-content: center;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-3);
  }
  
  .btn {
    width: 100%;
    max-width: 280px;
  }
  
  .features {
    padding: var(--spacing-8) 0;
  }
  
  .features .container {
    padding: 0 var(--spacing-4);
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
  
  .feature-card {
    padding: var(--spacing-6);
  }
  
  .feature-icon {
    width: 3.5rem;
    height: 3.5rem;
  }
  
  .feature-icon::before {
    width: 2rem;
    height: 2rem;
  }
  
  .cta .container {
    margin: 0 var(--spacing-4);
    padding: var(--spacing-8) var(--spacing-4);
  }
  
  .cta h2 {
    font-size: 2rem;
  }
  
  .learning-path .container {
    padding: 0 var(--spacing-4);
  }
  
  .path-steps {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
  
  .step {
    padding: var(--spacing-6);
  }
  
  .step-number {
    width: 48px;
    height: 48px;
    font-size: 1.125rem;
  }
  
  .nav-content {
    padding: 0 var(--spacing-4);
  }
  
  .nav-links {
    gap: var(--spacing-1);
    flex-wrap: wrap;
  }
  
  .code-preview {
    border-radius: 12px;
  }
  
  .code-content {
    padding: 16px;
  }
  
  .code-content pre {
    font-size: 13px;
  }
}
</style>