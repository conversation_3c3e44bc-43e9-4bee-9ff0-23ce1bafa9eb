<template>
  <div class="home-container">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-content">
        <div class="logo">
          <h1>{{ $t('home.nav.brand') }}</h1>
        </div>
        <div class="nav-links">
          <template v-if="!isLoggedIn">
            <router-link to="/login" class="nav-link">{{ $t('home.nav.studentLogin') }}</router-link>
            <router-link to="/register" class="nav-link">{{ $t('home.nav.register') }}</router-link>
          </template>
          <template v-else>
            <router-link v-if="isAdmin" to="/admin/dashboard" class="nav-link admin-link">{{ $t('home.nav.adminDashboard') }}</router-link>
            <router-link v-else to="/dashboard" class="nav-link">{{ $t('home.nav.dashboard') }}</router-link>
          </template>
          <router-link v-if="!isLoggedIn" to="/admin/login" class="nav-link admin-link">{{ $t('home.nav.admin') }}</router-link>
          <LanguageSwitcher />
        </div>
      </div>
    </nav>

    <!-- 英雄区域 -->
    <!-- 英雄区域 -->
    <section class="hero">
      <div class="hero-container">
        <div class="hero-content">
          <h1 class="hero-title">
            {{ $t('home.hero.title') }} <span class="highlight">{{ $t('home.hero.titleHighlight') }}</span> {{ $t('home.hero.titleEnd') }}
          </h1>
          <p class="hero-subtitle">
            {{ $t('home.hero.subtitle') }}<br>
            {{ $t('home.hero.subtitleLine2') }}
          </p>
          <div class="hero-buttons">
            <template v-if="!isLoggedIn">
              <router-link to="/register" class="btn btn-primary">
                {{ $t('home.hero.startLearning') }}
              </router-link>
              <router-link to="/login" class="btn btn-secondary">
                {{ $t('home.hero.hasAccount') }}
              </router-link>
            </template>
            <template v-else>
              <router-link v-if="isAdmin" to="/admin/dashboard" class="btn btn-primary">
                {{ $t('home.hero.goToAdminDashboard') }}
              </router-link>
              <router-link v-else to="/dashboard" class="btn btn-primary">
                {{ $t('home.hero.continueLearning') }}
              </router-link>
            </template>
          </div>
        </div>
        <div class="hero-image">
          <div class="code-preview">
            <div class="code-header">
              <span class="dot red"></span>
              <span class="dot yellow"></span>
              <span class="dot green"></span>
            </div>
            <div class="code-content">
              <pre><code>{{ $t('home.codePreview.comment1') }}
print("Hello, World!")

{{ $t('home.codePreview.comment2') }}
name = "{{ $t('home.codePreview.learnerName') }}"
age = 25
print(f"{{ $t('home.codePreview.ageOutput', { name: '{name}', age: '{age}' }) }}")

{{ $t('home.codePreview.comment3') }}
if age >= 18:
    print("{{ $t('home.codePreview.adultMessage') }}")
else:
    print("{{ $t('home.codePreview.childMessage') }}")
</code></pre>
            </div>
          </div>
        </div>
      </div>
    </section>    <!-- 功能特色 -->
    <section class="features">
      <div class="container">
        <h2 class="section-title">{{ $t('home.features.title') }}</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">💻</div>
            <h3>{{ $t('home.features.zeroSetup.title') }}</h3>
            <p>{{ $t('home.features.zeroSetup.description') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">⚡</div>
            <h3>{{ $t('home.features.instantFeedback.title') }}</h3>
            <p>{{ $t('home.features.instantFeedback.description') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🎯</div>
            <h3>{{ $t('home.features.personalizedLearning.title') }}</h3>
            <p>{{ $t('home.features.personalizedLearning.description') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🏗️</div>
            <h3>{{ $t('home.features.progressiveTeaching.title') }}</h3>
            <p>{{ $t('home.features.progressiveTeaching.description') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">💡</div>
            <h3>{{ $t('home.features.smartHints.title') }}</h3>
            <p>{{ $t('home.features.smartHints.description') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📊</div>
            <h3>{{ $t('home.features.learningTracking.title') }}</h3>
            <p>{{ $t('home.features.learningTracking.description') }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 学习路径 -->
    <section class="learning-path">
      <div class="container">
        <h2 class="section-title">{{ $t('home.learningPath.title') }}</h2>
        <div class="path-steps">
          <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h3>{{ $t('home.learningPath.step1.title') }}</h3>
              <p>{{ $t('home.learningPath.step1.description') }}</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h3>{{ $t('home.learningPath.step2.title') }}</h3>
              <p>{{ $t('home.learningPath.step2.description') }}</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h3>{{ $t('home.learningPath.step3.title') }}</h3>
              <p>{{ $t('home.learningPath.step3.description') }}</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">4</div>
            <div class="step-content">
              <h3>{{ $t('home.learningPath.step4.title') }}</h3>
              <p>{{ $t('home.learningPath.step4.description') }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 行动号召 -->
    <section class="cta" v-if="!isLoggedIn">
      <div class="container">
        <h2>{{ $t('home.cta.title') }}</h2>
        <p>{{ $t('home.cta.subtitle') }}</p>
        <router-link to="/register" class="btn btn-primary btn-large">
          {{ $t('home.cta.registerNow') }}
        </router-link>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>{{ $t('home.footer.brand') }}</h3>
            <p>{{ $t('home.footer.brandSubtitle') }}</p>
          </div>
          <div class="footer-section">
            <h4>{{ $t('home.footer.quickLinks') }}</h4>
            <ul>
              <li v-if="!isLoggedIn">
                <router-link to="/login">{{ $t('home.footer.studentLogin') }}</router-link>
              </li>
              <li v-if="!isLoggedIn">
                <router-link to="/register">{{ $t('home.footer.registerAccount') }}</router-link>
              </li>
              <li v-if="isLoggedIn && !isAdmin">
                <router-link to="/dashboard">{{ $t('home.footer.dashboard') }}</router-link>
              </li>
              <li v-if="isLoggedIn && isAdmin">
                <router-link to="/admin/dashboard">{{ $t('home.footer.adminDashboard') }}</router-link>
              </li>
              <li v-if="!isLoggedIn">
                <router-link to="/admin/login">{{ $t('home.footer.adminLogin') }}</router-link>
              </li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>{{ $t('home.footer.learningSupport') }}</h4>
            <ul>
              <li><a href="#">{{ $t('home.footer.userGuide') }}</a></li>
              <li><a href="#">{{ $t('home.footer.faq') }}</a></li>
              <li><a href="#">{{ $t('home.footer.contactUs') }}</a></li>
            </ul>
          </div>
        </div>
        <div class="footer-bottom">
          <p>{{ $t('home.footer.copyright') }}</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import LanguageSwitcher from '../components/LanguageSwitcher.vue'

const { t } = useI18n()

// 检查用户登录状态
const isLoggedIn = computed(() => {
  return !!localStorage.getItem('authToken')
})

const isAdmin = computed(() => {
  const userData = localStorage.getItem('user')
  if (!userData) return false
  try {
    const user = JSON.parse(userData)
    return user.role === 'admin'
  } catch {
    return false
  }
})
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: var(--color-slate-50);
}

/* 导航栏 */
.navbar {
  background: white;
  border-bottom: 1px solid var(--color-slate-200);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(8px);
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
}

.logo h1 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-slate-900);
  letter-spacing: -0.025em;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.nav-link {
  text-decoration: none;
  color: var(--color-slate-600);
  font-weight: 500;
  font-size: 0.875rem;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  transition: all 0.15s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--color-slate-900);
  background-color: var(--color-slate-100);
}

.admin-link {
  color: var(--color-amber-600) !important;
}

.admin-link:hover {
  background-color: var(--color-amber-50) !important;
}

/* 英雄区域 */
.hero {
  padding: var(--spacing-12) 0;
  background: var(--color-slate-50);
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-12) var(--spacing-6);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-12);
  align-items: center;
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-slate-200);
}

.hero-content {
  color: var(--color-slate-900);
}

.hero-title {
  font-size: 3rem;
  margin-bottom: var(--spacing-5);
  line-height: 1.2;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.highlight {
  background: linear-gradient(135deg, var(--color-blue-600), var(--color-purple-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  font-weight: 800;
}

.hero-subtitle {
  font-size: 1.125rem;
  margin-bottom: var(--spacing-8);
  color: var(--color-slate-600);
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-4);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3) var(--spacing-6);
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.15s ease;
  border: 1px solid transparent;
}

.btn-primary {
  background: var(--color-blue-600);
  color: white;
  border-color: var(--color-blue-600);
}

.btn-primary:hover {
  background: var(--color-blue-700);
  border-color: var(--color-blue-700);
}

.btn-secondary {
  background: transparent;
  color: var(--color-slate-700);
  border-color: var(--color-slate-300);
}

.btn-secondary:hover {
  background: var(--color-slate-100);
  border-color: var(--color-slate-400);
}

.btn-large {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: 1rem;
  font-weight: 600;
}

/* 代码预览 */
.code-preview {
  background: #1e1e1e;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.code-header {
  background: #2d2d2d;
  padding: 12px 16px;
  display: flex;
  gap: 8px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.dot.red { background: #ff5f57; }
.dot.yellow { background: #ffbd2e; }
.dot.green { background: #28ca42; }

.code-content {
  padding: 20px;
}

.code-content pre {
  margin: 0;
  color: #e6e6e6;
  font-family: 'Fira Code', monospace;
  line-height: 1.6;
}

/* 功能特色 */
.features {
  padding: var(--spacing-12) var(--spacing-6);
  background: var(--color-slate-50);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: var(--spacing-12);
  color: var(--color-slate-900);
  font-weight: 700;
  letter-spacing: -0.025em;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-6);
}

.feature-card {
  background: white;
  padding: var(--spacing-8);
  border-radius: var(--radius-xl);
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid var(--color-slate-200);
  box-shadow: var(--shadow-sm);
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-slate-300);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-5);
}

.feature-card h3 {
  color: var(--color-slate-900);
  margin-bottom: var(--spacing-3);
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.feature-card p {
  color: var(--color-slate-600);
  line-height: 1.6;
  font-size: 0.875rem;
}

/* 学习路径 */
.learning-path {
  padding: var(--spacing-12) 0;
  background: white;
}

.learning-path .container {
  padding: 0 var(--spacing-6);
}

.path-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-6);
}

.step {
  background: var(--color-slate-50);
  padding: var(--spacing-8);
  border-radius: var(--radius-xl);
  text-align: center;
  border: 1px solid var(--color-slate-200);
  transition: all 0.3s ease;
}

.step:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-slate-300);
}

.step-number {
  background: var(--color-blue-500);
  color: white;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.125rem;
  margin: 0 auto var(--spacing-5);
}

.step-content h3 {
  color: var(--color-slate-900);
  margin-bottom: var(--spacing-3);
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.step-content p {
  color: var(--color-slate-600);
  line-height: 1.6;
  font-size: 0.875rem;
}

/* 行动号召 */
.cta {
  padding: var(--spacing-12) 0;
  background: var(--color-slate-50);
}

.cta .container {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-slate-200);
  padding: var(--spacing-12) var(--spacing-6);
  text-align: center;
}

.cta h2 {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-5);
  color: var(--color-slate-900);
  font-weight: 700;
  letter-spacing: -0.025em;
}

.cta p {
  font-size: 1.125rem;
  margin-bottom: var(--spacing-8);
  color: var(--color-slate-600);
  line-height: 1.6;
}

/* 页脚 */
.footer {
  background: var(--color-slate-800);
  color: white;
  padding: 40px 20px 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 30px;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 15px;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section ul li {
  margin-bottom: 8px;
}

.footer-section a {
  color: var(--color-slate-400);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: white;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid var(--color-slate-700);
  color: var(--color-slate-400);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
    text-align: center;
    padding: var(--spacing-8) var(--spacing-4);
    margin: 0 var(--spacing-4);
  }
  
  .hero-title {
    font-size: 2.25rem;
  }
  
  .hero-buttons {
    justify-content: center;
    flex-direction: column;
    align-items: center;
  }
  
  .features {
    padding: var(--spacing-8) 0;
  }
  
  .features .container {
    padding: 0 var(--spacing-4);
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
  
  .cta .container {
    margin: 0 var(--spacing-4);
    padding: var(--spacing-8) var(--spacing-4);
  }
  
  .cta h2 {
    font-size: 2rem;
  }
  
  .learning-path .container {
    padding: 0 var(--spacing-4);
  }
  
  .path-steps {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
  
  .nav-content {
    padding: 0 var(--spacing-4);
  }
  
  .nav-links {
    gap: var(--spacing-1);
  }
}
</style>