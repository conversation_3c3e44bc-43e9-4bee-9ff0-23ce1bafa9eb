<template>
  <div class="admin-login-container">
    <div class="login-card">
      <div class="login-header">
        <h2>🔐 {{ $t('admin.login.title') }}</h2>
        <p>{{ $t('admin.login.platformSubtitle') }}</p>
      </div>

      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label for="username">{{ $t('auth.username') }}</label>
          <input
            id="username"
            v-model="loginForm.username"
            type="text"
            :placeholder="$t('admin.login.usernamePlaceholder')"
            required
            class="form-input"
          />
        </div>

        <div class="form-group">
          <label for="password">{{ $t('auth.password') }}</label>
          <input
            id="password"
            v-model="loginForm.password"
            type="password"
            :placeholder="$t('auth.passwordPlaceholder')"
            required
            class="form-input"
          />
        </div>

        <button type="submit" class="login-btn" :disabled="loading">
          {{ loading ? $t('auth.loggingIn') : $t('auth.login') }}
        </button>
      </form>

      <div class="login-footer">
        <router-link to="/login" class="student-link">
          👨‍🎓 {{ $t('admin.login.studentLogin') }}
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { authAPI } from '../api.js'

export default {
  name: 'AdminLogin',
  setup() {
    const { t } = useI18n()
    const router = useRouter()
    const loading = ref(false)
    const loginForm = ref({
      username: '',
      password: ''
    })

    const handleLogin = async () => {
      try {
        loading.value = true
        
        const response = await authAPI.login(loginForm.value)
        
        // 检查是否为管理员
        if (response.user.role !== 'admin') {
          window.showNotification(t('admin.login.adminOnlyError'), 'error')
          return
        }
        
        // 登录成功，跳转到管理员仪表板
        window.showNotification(t('auth.loginSuccess'), 'success')
        router.push('/admin/dashboard')
        
      } catch (error) {
        console.error('管理员登录失败:', error)
        const message = error.response?.data?.error || t('auth.loginError')
        window.showNotification(message, 'error')
      } finally {
        loading.value = false
      }
    }

    return {
      loginForm,
      loading,
      handleLogin,
      t
    }
  }
}
</script>

<style scoped>
.admin-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-slate-50);
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 40px rgba(0,0,0,0.1);
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: var(--color-slate-800);
  margin-bottom: 8px;
}

.login-header p {
  color: var(--color-slate-600);
  font-size: 14px;
}

.login-form {
  space-y: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: var(--color-slate-800);
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--color-slate-200);
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--color-blue-500);
}

.login-btn {
  width: 100%;
  background: linear-gradient(135deg, var(--color-blue-500) 0%, var(--color-blue-600) 100%);
  color: white;
  border: none;
  padding: 14px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.login-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid var(--color-slate-200);
}

.student-link {
  color: var(--color-blue-500);
  text-decoration: none;
  font-size: 14px;
}

.student-link:hover {
  text-decoration: underline;
}
</style>