# File: backend/app.py - Application Factory Pattern Example
def create_app():
    """Application factory function"""
    app = Flask(__name__)
    
    # Configuration management
    app.config.from_object(get_config())
    
    # Initialize extensions
    db.init_app(app)
    jwt.init_app(app)
    CORS(app, origins=config.CORS_ORIGINS)
    
    # Register blueprints
    from routes.auth import auth_bp
    # ... other blueprint imports
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    # ...
    
    return app

# File: backend/models/learning_models.py - Submission model example
class Submission(db.Model):
    """Submission record model - Core component of the Interaction Model"""
    __tablename__ = 'submissions'
    
    submission_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=False)
    exercise_id = db.Column(db.<PERSON>, db.<PERSON><PERSON>ey('exercises.exercise_id'), nullable=False)
    code = db.Column(db.Text, nullable=False)
    is_correct = db.Column(db.<PERSON>, nullable=False)
    # ... other field definitions
    
    # Relationship settings
    user = db.relationship('User', backref='submissions')

# File: backend/routes/submissions.py - Core submission workflow
@submissions_bp.route('/submit', methods=['POST'])
@jwt_required()
def submit_code() -> Tuple[Dict, int]:
    user_id = int(get_jwt_identity())
    data = request.get_json()
    # ... (input validation omitted)

    try:
        # 1. Retrieve exercise and code
        exercise_id = data['exercise_id']
        code = data['code']
        exercise = Exercise.query.get(exercise_id)
        
        # 2. Execute code and evaluate correctness
        executor = CodeExecutor(...)
        result = executor.execute_code(code, exercise.test_cases)
        is_correct = result.get('is_correct', False)
        
        # 3. Analyze code for feedback
        analyzer = CodeAnalyzer(...)
        analysis_result = analyzer.analyze_code(code, exercise.problem_statement)
        
        # 4. Save submission record
        submission = Submission(
            user_id=user_id, exercise_id=exercise_id, code=code,
            is_correct=is_correct, error_message=result.get('error_message'),
            output=result.get('output'), exec_time_sec=result.get('exec_time')
        )
        db.session.add(submission)
        
        # 5. Update user progress
        progress = UserProgress.query.filter_by(user_id=user_id, lesson_id=exercise.lesson_id).first()
        progress.increment_attempts()
        if is_correct:
            progress.complete_lesson()
        
        # 6. Generate comprehensive feedback
        feedback = analyzer.generate_comprehensive_feedback(result, analysis_result, progress)
        
        db.session.commit()
        
        return {'submission': submission.to_dict(), 'result': feedback}, 200
        
    except Exception as e:
        db.session.rollback()
        # ... (error handling)

 # File: backend/utils/code_analyzer.py - AI Feedback Engine via OpenRouter API
class CodeAnalyzer:
    def __init__(self, language='en'):
        self.config = get_config()
        self.openrouter_api_key = self.config.OPENROUTER_API_KEY
        self.openrouter_base_url = self.config.OPENROUTER_BASE_URL
        self.models = {
            'openai': 'openai/gpt-4o-mini',
            'deepseek': 'deepseek/deepseek-chat-v3-0324'
        }

    def analyze_code(self, code: str, problem_statement: str) -> Dict[str, Any]:
        """Comprehensive code analysis with local and AI-powered feedback"""
        results = {}
        
        # 1. Local static analysis
        results['local_analysis'] = self._local_static_analysis(code)
        
        # 2. AI analysis via OpenRouter API
        api_results = {}
        if self.openrouter_api_key and self.openrouter_api_key.startswith('sk-'):
            try:
                api_results['openai'] = self._analyze_with_openai(code, problem_statement)
            except Exception as e:
                api_results['openai'] = {'error': str(e), 'disabled': True}
        
        results['api_analysis'] = api_results
        results['summary'] = self._generate_summary(results)
        return results

    def _analyze_with_openai(self, code: str, problem_statement: str) -> Dict[str, Any]:
        """Call OpenAI model via OpenRouter for educational feedback"""
        messages = [
            {"role": "system", "content": "You are a Python programming tutor providing educational feedback."},
            {"role": "user", "content": f"Problem: {problem_statement}\n\nStudent code:\n{code}"}
        ]
        
        headers = {
            'Authorization': f'Bearer {self.openrouter_api_key}',
            'Content-Type': 'application/json'
        }
        payload = {
            'model': self.models['openai'],
            'messages': messages,
            'max_tokens': 800
        }
        
        response = requests.post(
            f'{self.openrouter_base_url}/chat/completions',
            headers=headers, json=payload, timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            feedback = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            return {'feedback': feedback, 'model': 'gpt-4o-mini'}
        else:
            raise Exception(f"OpenRouter API error: {response.status_code}")
        
