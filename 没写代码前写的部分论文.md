2.Literature Review
2.1Introduction
This chapter provides a comprehensive review of existing literature on online programming education and intelligent tutoring systems, in order to establish the theoretical and practical context for this project. The review follows a “funnel” approach: beginning with the broad landscape of e-learning for programming, then narrowing to specific challenges faced by novice learners and relevant educational theories, and finally examining current platforms and identifying a gap that motivates the present work. The chapter first explores the rise of online programming learning platforms and the importance of interactive practice. It then discusses foundational pedagogical theories – notably <PERSON>y<PERSON>sky’s Zone of Proximal Development and the concept of scaffolding – as well as the critical role of timely feedback for novices. Next, it introduces a framework for analyzing intelligent learning environments, focusing on the learner model, content (domain) model, and interaction (tutoring) model. Using this framework, a critical evaluation of several existing learning platforms (e.g. Codecademy, freeCodeCamp) is presented to highlight their strengths and limitations. Finally, the chapter identifies a specific gap in the current landscape – namely, the lack of adaptive, personalized support for novice programmers – and justifies how our project will address this gap.
2.2The Landscape of Online Programming Education
In recent years, programming education has increasingly moved from traditional classrooms to online environments. Massive Open Online Courses (MOOCs) and interactive coding websites have surged in popularity, making programming instruction widely accessible. For example, platforms like Codecademy and freeCodeCamp have attracted millions of learners by offering free, self-paced coding lessons with hands-on exercises. Codecademy, in particular, is known for its interactive coding tutorials designed to make learning to code engaging and beginner-friendly. These platforms typically combine short instructional texts or videos with an in-browser code editor, allowing learners to write and run code immediately. This immediate practice approach aligns with the well-established principle that learning-by-doing is crucial for mastering programming skills[1]. Studies have shown that active engagement through solving programming exercises leads to better knowledge retention and skill development than passive reading or watching lectures[2]. Interactive e-learning tools have also been linked to improved motivation and performance. For instance, a study found that students who used an online coding resource scored significantly higher on programming tests than those who learned via traditional methods, with a 20-point gain vs. 8-point gain in the control group[3]. Such findings underscore that providing plentiful hands-on practice and instant feedback in online platforms can substantially enhance learners’ success.

Despite their benefits and popularity, online coding platforms also highlight specific challenges in programming education. Novice programmers often struggle with abstract concepts, complex syntax, and debugging – difficulties that can be exacerbated when learning independently online. A common obstacle in MOOCs and websites is the lack of immediate, personalized support when a learner gets stuck. Traditional classroom settings allow students to ask instructors or peers for help in real time, whereas a solo online learner facing a confusing error might become frustrated or demotivated. Research indicates that novices can quickly lose confidence without timely guidance, contributing to high dropout rates in introductory programming courses[4]. Consequently, there is a growing recognition that simply providing content and exercises is not enough; the quality of interaction and feedback in these platforms is critical. Modern online coding education is thus increasingly informed by findings from educational psychology and learning science to keep beginners engaged[5]. Many platforms incorporate features like progress visualizations, badges, or basic hint systems to encourage persistence. Gamification elements (points, challenges) and community forums for peer support (as used in freeCodeCamp) are also employed to mitigate feelings of isolation. However, as will be discussed, the depth of pedagogical support in these platforms often remains limited[6]. This sets the stage for examining theoretical frameworks that can inform better designs.

2.3Foundational Pedagogical Theories for Novice Programmers
Effective learning environments for novice programmers draw on several foundational pedagogical theories. Three concepts are particularly relevant to this project’s design: (1) Vygotsky’s Zone of Proximal Development and the related notion of scaffolding, and (2) the importance of immediate, formative feedback.
2.3.1Zone of Proximal Development (ZPD) and Scaffolding
Vygotsky's theory identifies the ZPD as the crucial gap between what a learner can do independently and what they can achieve with guidance[7]. This concept is operationalized through scaffolding, a term coined to describe temporary, adaptive support that is gradually withdrawn as the learner gains competence[8]. For novice programmers, this support can manifest as hints, problem breakdowns, or partially completed examples. The key is that assistance must be minimal—just enough to enable the learner's next step without giving away the solution, thereby encouraging active problem-solving. The principle of scaffolding is a core pedagogical foundation for this project; the system will monitor learner performance and provide graduated, "just-in-time" and "just-enough" prompts when it detects struggle. As the learner improves, this support fades to promote independence.

2.3.2 Immediate and Formative Feedback

Another pillar of effective instruction is timely, specific feedback. Research has consistently shown that feedback is one of the most powerful influences on achievement, especially when it is formative—aimed at guiding improvement—and delivered immediately[9].A comprehensive review of the topic emphasizes that immediate feedback helps learners correct misunderstandings before they become ingrained, which is vital in programming where flawed mental models can persist[10]. Standard compiler errors, which are often cryptic and not pedagogically oriented, act as a barrier to persistence. A system that can intercept mistakes and provide clear, helpful cues can therefore dramatically improve the learning experience. The benefits of such adaptive feedback were demonstrated in a controlled study where an environment providing real-time, tailored hints significantly increased student engagement and their intent to persist in computer science [4]. The study also confirmed that well-designed feedback can reduce novice anxiety and foster a growth mindset, with students reporting the experience as more enjoyable and focused[4].

Therefore, the pedagogical groundwork for this project is clear: to create an optimal learning platform that operates within the learner’s ZPD by using scaffolded support and providing instant, informative feedback. The system is designed to continuously assess performance metrics (e.g., attempts, errors, time spent) and use these as cues to replicate the timely interventions of a human tutor, preventing frustration while fostering independence.

2.4Core Models of Intelligent Tutoring Systems (ITS)
To situate our proposed system within established educational-technology research, its design is grounded in the widely accepted architecture of Intelligent Tutoring Systems (ITS). An effective ITS is typically conceptualized as comprising three core components: a Learner Model, a Domain (Content) Model, and a Tutoring (Interaction) Model [11-13]. This separation of concerns is vital for designing intelligent learning environments, as it clarifies how the system uses different data: the domain model provides what to teach, the learner model tracks to whom it is being taught, and the tutoring model decides how to teach next[14].

Learner Model. A dynamic profile that represents the student’s current knowledge, skills, and learning history. Its primary role is to enable personalization by tracking what the student knows and does not know, often as an “overlay” of the expert domain knowledge [15]. Our project implements this as a simple yet effective overlay model, recording each user’s progress (e.g., which lessons are completed), performance metrics (e.g., number of attempts), and engagement patterns (e.g., time taken per exercise). This data is crucial for the tutoring model to make informed, adaptive decisions.

Domain (Content) Model. Encodes the subject matter to be taught—in our case, Python fundamentals. It is a structured representation of concepts, exercises, and, critically, the prerequisite relationships between them (e.g., “variables” must be mastered before “loops”) [13, 16]. This ensures a logical learning progression. In our system, the domain model is realized as a structured curriculum of lessons and exercises. Each exercise is annotated with a set of automated test cases for immediate evaluation and a list of tiered hints to support scaffolding. This model not only defines the content but also provides the resources for assessment and intervention.

Tutoring (Interaction) Model. Often called the pedagogical model, it functions as the system’s “teaching brain.” It uses rules or algorithms to interpret data from the other two models and decide on the next instructional action [12, 17]. This model operationalizes pedagogical theories such as scaffolding and mastery learning. Our system’s tutoring model is implemented as a set of direct, rule-based heuristics that govern the platform’s interactivity. For instance, it enforces the prerequisite logic from the domain model, unlocking content only when a user’s learner model shows completion of the required prior lesson.

Crucially, this model orchestrates the feedback loop. To illustrate, consider how the models work in concert: when a student submits an incorrect solution, the system does not simply return a raw compiler error. Instead, the tutoring model intervenes. It references the learner model to see whether this is the first or a repeated attempt. Based on this context, it might select a specific, targeted hint from the domain model’s pre-authored list. For example, a rule might state: “If the learner model shows more than three failed attempts on an exercise, provide the first hint from the domain model.” After a successful submission, the tutoring model provides positive reinforcement and signals the learner model to update the student’s progress, marking the concept as “mastered.”

By explicitly adopting this three-model framework, we ensure our system’s design is not based on ad-hoc decisions but is guided by decades of proven ITS research [12, 17]. This structure provides a clear rationale for our implementation choices, detailed in the next chapter, and lends credibility to our approach by aligning it with established paradigms for creating effective, adaptive learning environments.

2.5Critical Evaluation of Existing Systems
With the theoretical framework established, this section critically evaluates representative online programming platforms through the analytical lens of the Learner, Content, and Interaction models. The objective is to identify common strengths and, more importantly, pedagogical weaknesses, thereby locating the research gap our project aims to address.
2.5.1Codecademy
Codecademy serves as a prime example of a platform with a highly-refined Content Model but a less developed Interaction and Learner Model. Its primary strength lies in a well-structured, linear curriculum that breaks down complex topics into "bite-sized" lessons[18]. This incremental approach, combined with an interactive coding environment and gamified elements like progress streaks, is effective for user engagement and introducing basic syntax[19].

However, a critical analysis of its pedagogical support reveals significant limitations. The platform's Interaction Model primarily functions as a simple pass/fail evaluator, checking a user's code against a predetermined solution. It offers limited adaptive feedback or scaffolded hints that guide a learner through their specific misunderstanding. This is largely because its Learner Model is rudimentary, tracking little more than lesson completion status. It does not capture crucial performance metrics—such as repeated attempts or common error patterns—that would be necessary to infer a learner's cognitive state and trigger a more supportive intervention.
This "one-size-fits-all" approach has been a subject of criticism in the literature. Research has highlighted that while Codecademy's "hand-holding" style makes the initial learning curve smooth, it can fail to cultivate independent problem-solving skills [18]. Learners may develop an "illusion of competence," successfully completing syntax puzzles within the platform's constraints but struggling to apply their knowledge to novel, real-world problems. The system's inability to adapt means it is simultaneously too rigid for advanced learners and potentially unsupportive for novices who require more than generic error messages to overcome conceptual hurdles.
In conclusion, while Codecademy provides an accessible and engaging entry point to programming, its pedagogical model lacks the depth to provide true adaptive scaffolding. The shallow integration between its Interaction and Learner Models highlights a clear opportunity for systems that can offer more personalized, data-driven, and pedagogically-informed guidance to foster robust and transferable programming skills.

2.5.2freeCodeCamp

freeCodeCamp offers a different pedagogical approach, leveraging a project-oriented Content Model to enhance skill transfer, but it shares with Codecademy similarly underdeveloped Learner and Interaction Models. Its strength lies in guiding learners to apply fundamental concepts in tangible portfolio projects, addressing a common criticism of other platforms that exercises can feel abstract and isolated. This emphasis on practical application is a significant pedagogical advantage[20].

However, the platform's adaptive support mechanism is minimal. Like Codecademy, its Learner Model is rudimentary, primarily tracking challenge completion rather than diagnosing misconceptions. Consequently, its Interaction Model cannot offer personalized, adaptive feedback. While it provides immediate feedback via pass/fail test cases, this is often insufficient for a struggling novice[21]. The platform's solution to this is to outsource pedagogical support to its community forum. Rather than the system providing scaffolded hints, the learner is expected to seek help from peers or static articles when they get stuck .

This reliance on external support highlights the central weakness: freeCodeCamp excels at providing curriculum scaffolding (a logical progression of projects with increasing complexity) but lacks adaptive scaffolding at the moment-to-moment problem-solving level. A learner encountering a common bug must rely on their own debugging skills or the availability and quality of community help, a process that can lead to significant frustration and attrition. Therefore, while its project-based model is valuable, freeCodeCamp exemplifies the gap this project seeks to fill: the absence of an integrated, intelligent tutoring layer that can provide real-time, context-aware guidance when a learner needs it most[22].

2.5.3Other Intelligent Tutoring Systems for Programming

Beyond commercial platforms, academic research in Intelligent Tutoring Systems (ITS) provides valuable insights into more sophisticated models of adaptivity. These systems, however, often employ different underlying approaches. A notable example is ITAP (Intelligent Teaching Assistant for Programming), a data-driven system that generates hints from mined solution spaces [23]. Rather than relying on a pre-authored knowledge base, ITAP functions by abstracting a student's code into a state within a "solution space" graph. It then automatically generates a path from the student's current incorrect state to the nearest correct one, producing a targeted, next-step hint. While the authors note that a full evaluation with students has not yet been performed, technical evaluations demonstrate that the system can generate viable hints from almost any intermediate state of a student's code, showing high coverage and potential [23].

Another line of research has explored Parsons Problems, which require learners to arrange mixed-up code blocks into the correct order. These tutors implement scaffolding through pre-set difficulty levels, such as by adding distractor blocks to increase complexity or by providing the initial block to reduce it, rather than through real-time dynamic adaptation based on performance. Studies have found that this form of scaffolding helps novices learn programming constructs more effectively than writing code from scratch with no guidance [24].

While more advanced systems, represented by examples like ITAP, are closer to implementing the full three-model ITS architecture, many research prototypes like Parsons Problems tutors primarily focus on task-level scaffolding without a persistent learner model. A common trade-off is that these sophisticated academic systems are often domain-specific and resource-intensive to develop, which explains their limited adoption by scalable commercial platforms. This leads to a critical insight: a gap persists between the engaging, scalable nature of mainstream platforms and the pedagogically powerful but limited scope of ITS prototypes. This conclusion is strongly supported by a systematic review of 101 programming learning tools, which found that the majority provided only basic error feedback, with little personalization or guidance on how to improve [25]. This clearly indicates a space for innovation for a platform that integrates the user-friendliness of popular MOOCs with the adaptive, supportive feedback mechanisms demonstrated in ITS literature.

To visually synthesize the preceding analysis, Table 2.1 provides a side-by-side comparison of these representative systems across several key pedagogical dimensions.
Feature / Dimension	Codecademy	freeCodeCamp	Academic ITS Prototype(e.g., ITAP)	Our Proposed System
Content Model	Structured, linear micro-lessons with in-browser exercises.	Project-driven curriculum that culminates in portfolio projects.	Narrow, research-specific domain with fine-grained concept mapping.	Modular curriculum; every Knowledge Component is tied to auto-graded exercises and tiered hints.
Learner Model	Basic overlay—tracks only lesson completion.	Certification-oriented—tracks finished challenges & projects.	Fine-grained knowledge tracing; infers mastery and solution paths.	Enhanced overlay—records completion plus attempts, average time, and common error types for intervention.
Interaction & Feedback	Immediate but generic pass/fail; compiler errors largely unchanged.	Immediate but non-adaptive; pass/fail from unit tests, community forum for help.	Highly targeted: generates next-step hints from the learner’s exact code state.	Rule-based adaptive engine: error-specific guidance (e.g., SyntaxError), proactive hint button when attempts > 3, etc.
Scaffolding & Adaptivity	Low—one-size-fits-all path, static hints.	Low—project progression but no real-time personalization.	High—graded hints that fade as competence grows.	Medium–High—dynamic, graded scaffolding triggered by learner metrics; aims to bridge commercial ease with ITS rigor.
Key Strength	Very low entry barrier; strongly guided onboarding.	Emphasizes real-world application and skill transfer.	Theoretically the most effective personalized guidance.	Combines mainstream usability with intelligent tutoring—adaptive support for true novices.
Key Limitation	May foster an “illusion of competence”; weak independent problem-solving.	No built-in tutoring; high risk of frustration when stuck.	Resource-intensive; hard to scale beyond a narrow domain.	Effectiveness depends on quality of author-defined rules and pedagogical content.
Table 2.1: A Comparative Analysis of Online Programming Platforms and the Proposed System
As visually synthesized in Table 2.1, the current landscape of online programming education presents a distinct trade-off. Mainstream platforms like Codecademy and freeCodeCamp offer impressive scalability and accessibility but often lack deep, personalized pedagogical support. Conversely, academic ITS prototypes demonstrate powerful adaptive mechanisms but remain difficult to scale. This dichotomy highlights a clear and compelling gap for a system that can bridge these two worlds.

2.6Identifying the Research Gap and Justifying the Project

Drawing directly from the insights synthesized in Table 2.1, the research gap this project addresses is the absence of a platform that successfully merges the broad accessibility of mainstream e-learning sites with the pedagogically-informed, adaptive support found in specialized Intelligent Tutoring Systems.

This deficiency manifests in several critical ways for novice learners, particularly those outside of computer science majors. On many large-scale platforms, they receive feedback that is merely binary (correct/incorrect) or overly generic, with minimal consideration of their individual learning needs. As a result, when beginners encounter obstacles, such as a confusing error or a difficult concept, the system offers little targeted assistance beyond showing a solution, which can lead to frustration, stagnation, or reliance on external help sources. From an educational theory standpoint, these platforms do not fully leverage scaffolding principles: they fail to provide "just-right" support in the Zone of Proximal Development, nor do they fade that support appropriately. Furthermore, their learner modeling is often primitive, typically limited to tracking progress rather than diagnosing misconceptions or adapting to a learner’s pace.
There is thus a demonstrable need for a programming learning system that can dynamically assess a learner’s difficulties and respond with personalized, scaffolded support. This system would monitor signals like repeated failed attempts, unusual delay in solving a problem, or common error patterns (as stored in a learner model), and use those to trigger helpful interventions via the interaction model. For instance, if a student has attempted an exercise multiple times and keeps encountering a SyntaxError, the system could proactively offer a hint about the correct syntax or point out the error location in a friendly manner. If a student’s average solving time on recent tasks is far above their norm, the system could suggest a review of prerequisite material or provide a more step-by-step breakdown of the current problem. Essentially, the gap is the absence of an “online coding tutor” that replicates the guidance a human teacher might provide during practice—encouraging, giving tips, and ensuring the student does not remain stuck for too long on any single hurdle.
Additionally, our review highlighted that even when hints are available in existing platforms, they are typically static and not context-aware. The literature suggests that making feedback context-specific and incremental (from gentle nudges to detailed explanations) is far more effective for learning[4, 25]. Therefore, another aspect of the gap is qualitative: it’s not just about having any feedback, but having the right kind of feedback at the right time. The system we propose aims to fill this qualitative gap by integrating research-backed feedback strategies (immediate, specific, and scaffolded) into the learning loop of an interactive coding website.
From a practical perspective, addressing this gap could significantly improve learning outcomes and learner retention. Beginners, particularly non-CS students, would benefit from a platform that not only teaches them syntax and concepts, but also teaches them how to learn and debug code. Instead of being left to either sink or find help elsewhere, novices would have a safety net that catches misconceptions early and guides them back on track. This supportive learning environment could reduce the frustration barrier that now causes many to abandon programming early in the learning process.

In conclusion, the unique contribution of this project is the development of a web-based programming learning platform that integrates a novice-centric adaptive tutoring mechanism into an interactive coding practice environment. By combining the scalability and content richness of existing e-learning platforms with the personalized feedback mechanisms found in intelligent tutoring systems, the project aims to create a more effective and engaging learning experience for beginners. This approach directly addresses the identified gap in literature and practice. No longer is the project just an arbitrary idea – it stands as a response to concrete shortcomings documented in prior work. In the next chapters, we will detail the design and implementation of this system, demonstrating how each component (learner model, content model, interaction model) has been realized to achieve the goal of filling this educational gap.

2.7Summary

In this chapter, we surveyed the landscape of online programming education and examined key educational theories relevant to teaching novices. We discussed how scaffolding and immediate feedback are critical for beginner engagement, and described the standard learner/content/tutoring model framework for intelligent learning systems. Through a critical analysis of existing platforms (Codecademy, freeCodeCamp, etc.), we identified that while current solutions excel in accessibility and content delivery, they often lack adaptive guidance for students when they struggle. This analysis led us to pinpoint a gap: the need for an interactive learning platform that provides personalized, scaffolded support to novice programmers. The chapter established the theoretical foundation and justification for our project – a system explicitly designed to fill this gap. In the next chapter, we transition from “what” and “why” to “how,” by outlining the requirements and design of the proposed solution in light of the insights gained here.




Chapter 3: Requirements Analysis and Project Management
3.1 Requirements Analysis 
本节旨在全面、系统地定义“交互式Python智能辅导平台”的功能性与非功能性需求。通过对利益相关者的分析、用例的构建以及具体需求的详细阐述，本章为后续的系统设计（第五章）与实现（第六章）提供了一个明确、可衡量且可验证的标准。需求分析过程充分借鉴了第二章中对现有系统局限性的评估，确保本项目的需求能够精准地弥补当前在线编程教育领域的空白。

3.1.1 Stakeholders and Use Cases
为了确保系统设计能够满足核心用户的需求，本项目识别出两类主要的利益相关者：学习者 (Learner) 和管理员 (Administrator)。
Learner (学习者)： 学习者被定义为编程初学者，特别是没有计算机科学背景的学生。他们的主要目标是在一个无障碍的环境中学习 Python 基础知识，并在遇到困难时获得即时且个性化的帮助。
Administrator (管理员/教育者)： 管理员被定义为教师或教学内容的维护者。他们的主要目标是能够通过平台方便、高效地创建和管理教学内容，并监控整体学习情况，以评估教学效果。
为了更清晰地描述这两类用户与系统之间的交互，本项目为每种角色分别构建了 UML 用例图。如图 3.1 所示，学习者角色的用例图展示了学习者在系统中的主要交互，包括账户注册/登录、查看学习仪表板、浏览课程目录、进入课程解决练习、提交代码、接收反馈，以及在遇到困难时请求提示等功能。相应地，图 3.2 展示了管理员角色的用例图，涵盖管理员所具备的关键功能，包括管理课程模块、课程内容、练习和测试用例，管理用户账户，以及查看平台的学习数据分析等。


Figure 3.1: Use Case Diagram for the Learner Role



Figure 3.2: Use Case Diagram for the Administrator Role
3.1.2 User Requirements
基于对上述利益相关者及其目标的分析，本项目提炼出以下核心用户需求。这些需求直接驱动了系统的功能设计，确保系统的开发紧密围绕最终用户的期望。

学习者（Learner）相关需求

UR1
  新用户应能够通过简单直观的注册与登录流程进入平台，并确保其学习进度得到可靠地保存和同步，保障数据安全性与持续性。

UR2
  学习者需要在界面上直观查看到完整的课程列表及各课程的个人完成状态，以便据此合理制定和调整自己的学习计划。

UR3
  平台应支持在同一页面同时呈现课程内容与代码练习编辑区，使学习者能够在阅读资料的同时立即动手实践，获得沉浸式学习体验。

UR4
  学习者无需本地环境配置即可在浏览器中直接运行代码，并即时获得程序输出或清晰易懂的错误提示，从而降低技术门槛、提高调试效率。

UR5
  当学习者在编程过程中遇到困难时，系统应提供循序渐进、可操作的提示（hints），帮助学习者逐步定位问题并找到解决思路，而非直接给出答案。

UR6
  平台应支持多种 AI 助手（例如 ChatGPT、DeepSeek 等）的快速切换，使学习者能够根据个人偏好与学习场景，选择最适合的智能反馈与指导。

管理员（Administrator）相关需求

UR7
  管理员需通过友好的图形化界面（GUI）而非直接操作数据库来创建、编辑和管理教学内容（包括模块、课程、练习等），以降低内容维护门槛并减少潜在错误。

UR8
  平台应向管理员提供整体学习数据统计与可视化分析，帮助其了解学生普遍的难点、课程效果及学习进度，以便持续改进教学设计和资源分配。
3.1.3 System Requirements (系统需求)
3.1.3.1 Functional Requirements (功能需求)
为满足上述用户需求，系统必须实现以下功能：
FR1: User Authentication (用户认证)： 系统应提供用户注册和登录功能，并使用 JWT（JSON Web Token）进行会话管理，以保障用户账户安全和学习数据的持久化存储。
FR2: Curriculum Management (课程管理)： 系统应允许管理员通过专用的后台界面对课程模块、课程内容、练习以及测试用例进行完整的 CRUD 操作（创建、读取、更新、删除），以确保教学内容能够被灵活维护和扩展。
FR3: Content Delivery (内容交付)： 系统应根据预设的课程依赖关系（先决条件）向学习者展示结构化的学习路径，保证学习者按照正确的知识顺序逐步解锁课程并学习，从而获得连贯的学习体验。
FR4: Code Execution and Assessment (代码执行与评估)： 系统应能在安全的沙箱环境中执行学习者提交的 Python 代码，并根据预设的测试用例（其中包括对用户隐藏的测试用例）自动评估代码的正确性，及时将结果反馈给学习者。
FR5: Multi-Source Feedback Generation (多源反馈生成)： 对于未通过测试的错误代码，系统应生成并提供多种反馈信息，包括：a) 基于代码运行结果的技术性分析反馈；b) 至少两种由不同大型语言模型（如 OpenAI GPT 和 DeepSeek）生成的教育性指导反馈，以便学习者能够自由切换查看不同风格的讲解。
FR6: Learner Progress Tracking (学习者进度追踪)： 系统应将每个学习者在每门课程和练习中的进度数据持久化存储在数据库中，包括完成状态、尝试次数以及平均解题用时等，以实现对学习者模型的记录和支持个性化教学。
FR7: Scaffolding Support (脚手架提示支持)： 系统应根据学习者的实际表现（例如尝试次数超过 3 次或平均用时超过 90 秒）动态提供分级提示，逐步引导学习者解决问题，以实践 Scaffolding 教学原则，避免学习者陷入长期的卡顿状态。
3.1.3.2 Non-Functional Requirements (非功能性需求)
除了上述核心功能性需求外，系统还必须满足以下关键的质量属性：
Performance (性能)： 从学习者提交代码到系统返回初步评判结果的端到端响应时间应控制在 10 秒以内，以确保提供接近即时反馈的用户体验。
Usability (可用性)： 系统界面设计必须简洁直观，并提供完整的中英双语支持，使来自不同语言背景的用户都能方便地使用平台进行学习。
Security (安全性)： 用户密码需采用行业标准的哈希算法加盐存储，确保账户信息安全。所有用户提交的代码必须在与主应用服务器隔离的安全沙箱环境中执行，以防止恶意代码对主系统造成影响，杜绝常见的代码注入等安全风险。
Reliability (可靠性)： 系统应具备对外部服务故障的容错能力。在系统实现中，当外部代码执行 API（如 Judge0 服务）不可用时，系统应自动降级切换至本地的代码执行模拟模式，确保练习评估功能不中断；同理，当 AI 分析服务不可用时，系统应回退至本地规则分析机制，以保证反馈机制的鲁棒性和核心功能的持续可用。
3.2 Project Management (项目管理)
本节展示了为确保项目能在规定时间范围内成功交付而采用的规划、执行和风险控制策略。
3.2.1 Project Scheduling (项目排期)
为了有效地管理项目的整个生命周期，本项目使用甘特图对主要任务进行了分解和时间规划。项目从 2025 年六月中旬开始，计划于 8 月下旬完成并提交最终报告。时间安排充分考虑了研究、设计、开发、测试以及论文撰写等各个阶段的工作量和顺序。特别地，根据导师的建议，为论文撰写和提交阶段预留了超过三周的充足时间，以应对开发阶段可能出现的延期，并确保报告的质量。


Figure 3.3: Project Gantt Chart
3.2.2 Risk Management (风险管理)
风险管理是确保项目成功的关键环节。本项目对项目中可能出现的技术和管理风险进行了识别、分析，并制定了相应的缓解计划，如下文所示。

R1 — 外部 API 服务中断（Judge0 / OpenRouter） 该风险的可能性为中等，影响为高。一旦代码执行服务或 AI 分析服务不可用，平台的核心功能将受威胁。为了应对这一问题，系统架构中预先设计了自动降级机制：当 Judge0 中断时，平台立刻切换至本地模拟执行模式；当 OpenRouter 不可用时，则启用本地规则分析模块。该方案可确保即使外部依赖失效，教学与评估流程仍能持续运行，从而将停机带来的负面影响降至最低。

R2 — AI 反馈质量不可控（生成不相关或错误的指导） 该风险的可能性为中等，影响为中等。若反馈质量失真，不仅影响学习效果，还可能削弱用户对平台的信任。为降低风险，系统采用多源反馈设计，引入 ChatGPT、DeepSeek 等多种 AI 助手，允许学习者交叉验证结果。同时集成用户评分系统，对每条 AI 反馈进行量化评价，长周期收集数据后用于模型微调与质量监控，持续提升答案的准确性与相关性。

R3 — 项目范围蔓延（Scope Creep）导致功能无法按时完成 该风险的可能性与影响均为高。功能过度扩张会分散开发资源，延误关键里程碑。项目团队因此坚持最小可行产品（MVP）开发原则，首先交付核心功能，并与导师定期召开评审会议，对需求变更进行严格审查与冻结，确保开发重点始终聚焦于论文和系统的核心目标。

R4 — 数据安全问题（如恶意代码提交） 尽管该风险的可能性较低，但其影响被评为高。一旦恶意代码突破防线，可能危及服务器安全甚至用户数据隐私。为了从根本上消除此隐患，平台将所有代码执行任务完全外包给专业沙箱服务 Judge0，使潜在恶意代码与主应用服务器隔离；同时采用强哈希算法存储用户密码，多重防线共同守护平台的安全性与可靠性。

Chapter 4: Technological Background 

本章旨在详细阐述构建“交互式Python智能辅导平台”所采用的技术栈、架构选型及其理论依据。通过对前后端框架、数据库、外部API服务等关键技术组件的分析与论证，本章为后续第五章的系统设计和第六章的具体实现提供了坚实的技术背景和理论支撑。本章的目标是证明，所选的技术方案不仅在技术上是可行的，而且在功能、性能、安全性和开发效率方面都是最适合本项目特定需求的。

4.2 System Architecture (系统架构)
4.2.1 Architectural Pattern: Decoupled Full-Stack Architecture (架构模式：前后端分离的全栈架构)
本项目采用现代Web开发中主流的前后端分离 (Decoupled) 架构。该架构将整个系统明确划分为两个独立但互补的部分：

前端 (Frontend): 一个基于Vue.js 3构建的单页应用 (Single-Page Application, SPA)，负责处理所有的用户界面渲染和客户端的交互逻辑。它作为一个独立的应用程序，完全运行在用户的浏览器中。

后端 (Backend): 一个基于Python Flask构建的无状态RESTful API服务，负责处理所有业务逻辑、数据持久化以及与外部服务的通信。

采用这种前后端分离的全栈架构带来了显著的工程优势：

模块化与可维护性 (Modularity & Maintainability): 前后端代码库完全分离，职责清晰。这种模块化设计降低了系统的整体复杂度，允许开发和部署各自独立进行。

提升用户体验 (Enhanced User Experience): SPA架构提供了如桌面应用般流畅的无刷新页面跳转体验。导航和交互几乎是即时的，这极大地提升了用户的参与感和满意度。

技术栈灵活性 (Technology Flexibility): 前端和后端可以自由选择最适合各自场景的技术，而不会互相制约。例如，本项目在客户端实现了现代化的Vue.js界面，而在服务器端则采用了轻量级的Flask API。


这一架构决策直接指导了后续的技术选型。例如，为了支持无状态的客户端，后端必须采用如JWT这样的令牌认证机制；为了实现前后端通信，必须设计一套标准化的RESTful API接口。本章后续部分将详细论述在这一架构下，本项目为各个组件选择的具体技术。

4.2 Backend Technology Stack (后端技术栈)
4.2.1 Core Framework: Python & Flask (核心框架：Python 与 Flask)
后端服务采用Python 3作为主要编程语言，并选择Flask 2作为Web框架。

Python: 选择Python是基于其简洁的语法和庞大的社区支持。更重要的是，Python与本项目的教学内容（教授Python编程）保持一致，并且其在数据科学和人工智能领域拥有无与伦比的生态系统，为未来扩展平台的智能功能提供了便利。

Flask: 在众多Python Web框架中，本项目选择了Flask（一个“微框架”）而非功能更全面的Django。Flask轻量且高度灵活，非常适合构建本项目所需的、以API为核心的服务。它没有强制的项目结构，允许本项目根据ITS三模型理论自由地构建清晰的项目结构，例如将业务逻辑、数据模型和工具类分别放在独立的模块中，从而实现高内聚、低耦合的设计。相比之下，Django虽然功能强大，但其庞大的体积和高度集成的特性对于本项目的规模而言显得过于笨重。

4.2.2 Database and ORM: SQLite & SQLAlchemy (数据库与ORM)
在数据库层面，本项目在开发阶段使用SQLite，并采用SQLAlchemy作为对象关系映射（ORM）工具：

SQLite: 在开发阶段，SQLite被用作数据库。它是一个基于文件的、零配置的数据库，极大地简化了开发环境的搭建和测试流程。

SQLAlchemy: 为了以高效、安全的方式与数据库交互，本项目使用了SQLAlchemy作为ORM。SQLAlchemy允许本项目将数据模型定义为Python类（例如User、Lesson等），并自动处理Python对象与数据库表记录之间的转换。这不仅消除了编写复杂原生SQL的需要，降低了SQL注入的风险，更重要的是，ORM提供了数据库无关性。本项目的代码不与特定的数据库绑定，未来可以通过简单地更改数据库连接字符串，无缝地将系统升级到生产级的数据库（如PostgreSQL），而无需修改任何数据模型代码。

4.2.3 Authentication: JWT (用户认证)
在前后端分离的架构下，传统的基于Session的认证方式不再适用。因此，本项目采用了基于**JSON Web Tokens (JWT)**的无状态认证机制，并利用Flask-JWT-Extended库来实现。该技术非常适合前后端分离的RESTful API架构，因为它无需服务器端存储会话，提高了系统的可扩展性。当用户登录成功后，后端会签发一个JWT令牌；前端将其存储在本地，并在后续的每次API请求中通过HTTP头部发送该令牌。后端通过验证令牌的签名来确认用户身份，无需在服务器端维护任何会getSession信息。

4.3 External Services Integration (外部服务集成)
4.3.1 Secure Code Execution Service: Judge0 (安全代码执行服务)
安全地执行用户提交的任意代码是本项目的核心技术挑战之一。直接在本项目的服务器上运行不受信任的代码是极其危险的。因此，出于对安全性和开发效率的考量，本项目做出了一个关键的工程决策：将代码执行功能外包给一个名为Judge0的第三方在线评测服务。Judge0提供了一个隔离的、安全的沙箱环境来运行代码，并通过RESTful API暴露其功能。这一选择使本项目能够避免在自有服务器上执行不受信任代码的巨大风险，从而保证了平台的安全与稳定。

4.3.2 AI Feedback Engine: OpenRouter (人工智能反馈引擎)
为了给用户提供高质量且多样化的学习反馈，平台集成了大型语言模型（LLM）的能力进行代码分析。为了简化集成过程并保持系统的可扩展性，本项目选择了OpenRouter作为AI模型的统一接口。

OpenRouter是一个API聚合器，它允许本项目通过一个统一的接口访问来自不同提供商的多种AI模型。这一技术选型带来了显著的工程优势：

简化开发： 无需为每个AI模型编写单独的API客户端。

灵活性与可扩展性： 可以轻松地在后端配置文件中切换或增加新的AI模型（例如，本项目同时集成了OpenAI的gpt-4o-mini和deepseek-chat），而无需修改核心代码。

成本与可靠性管理： OpenRouter提供了统一的计费和请求路由功能，便于未来进行成本控制。

通过使用OpenRouter，本项目能够专注于如何利用AI提升教学效果（即Prompt Engineering），而不是陷入繁琐的API集成工作中。

4.4 Frontend Technology Stack (前端技术栈)
4.4.1 Core Framework: Vue.js 3 (核心框架：Vue.js 3)
平台的前端是使用Vue.js 3框架构建的，并利用其组合式API (Composition API)来创建一个响应式的、动态的单页应用。选择Vue 3的主要原因是其组合式API，它使得在Lesson.vue这样复杂、状态繁多的组件中组织逻辑变得非常清晰和高效。本项目还使用了Vite作为构建工具和开发服务器，它提供了毫秒级的热模块重载，极大地提升了开发体验和效率。

4.4.2 Key Components and Libraries (关键组件与库)
本项目的前端技术栈通过以下几个关键库和组件得到了增强，以满足特定的用户需求：

Vue Router: 用于实现SPA的客户端路由，为用户在登录、仪表板、课程列表和课程详情等页面之间提供了流畅的无刷新导航体验。

CodeMirror 6: 为了满足用户在浏览器中进行专业编码的需求（UR4），本项目集成了CodeMirror 6作为嵌入式代码编辑器。它提供了语法高亮、自动缩进和主题切换等丰富功能，显著提升了编码体验。

Axios: 作为HTTP客户端，负责前端与后端API之间的所有通信。本项目配置了Axios的实例，以自动在每个请求头中附加JWT令牌，从而简化了API调用。

Marked: 用于将后端存储的Markdown格式的课程内容实时渲染成HTML，以便在前端进行富文本展示，满足了内容呈现的需求。

Vue-i18n: 为满足国际化需求，本项目使用Vue-i18n库实现了完整的前端国际化方案，支持中英双语切换，提升了系统的可用性和受众范围。

4.5 Summary of Technology Choices (技术选型总结)
综上所述，本项目的技术栈是经过深思熟虑的选择，旨在平衡开发效率、系统性能、安全性、可扩展性和用户体验。通过将一个轻量级的Python后端、一个现代化的Vue.js前端与专业的第三方服务相结合，本项目构建了一个技术上可行、功能上强大且架构上稳健的在线学习平台。这个技术方案不仅成功地支撑了项目所有需求的实现，也为未来的功能迭代和优化奠定了坚实的基础。

Chapter 5: Design (系统设计)
本章旨在详细阐述“交互式Python智能辅导平台”的系统设计方案。基于第四章对技术选型的论证，本章将从宏观的系统架构、中观的数据库结构，到微观的核心交互流程，全方位展示本项目的工程蓝图。本章的目标是提出一个与平台无关、结构清晰、易于扩展且便于维护的设计方案，为第六章的具体实现提供明确指导。整个设计过程遵循模块化、高内聚和低耦合等核心软件工程原则，以确保系统具有良好的鲁棒性并为未来迭代奠定基础。

5.1 System Architecture (系统架构)
本系统总体架构采用了在第四章已进行详细论证的前后端分离设计模式。基于该模式，本项目设计的具体系统架构如图5.1所示，它直观地展示了系统的四个核心组成部分及其相互关系。


Figure 5.1: High-Level System Architecture Diagram (高层系统架构图)

该架构在逻辑上划分为四个层次：

客户端 (Client): 完全运行于用户浏览器的Vue.js单页应用，负责所有用户界面的渲染和交互。

应用服务器 (Application Server): 系统的业务逻辑核心，由Flask构建的RESTful API服务组成。它实现了智能辅导系统（ITS）三大模型所需的规则，并作为中间层协调客户端、数据库和外部服务之间的通信。

数据存储 (Data Persistence): 独立的数据库层，用于持久化存储平台的所有数据，包括用户信息、课程内容以及学习进度等。

外部服务 (External Services): 将某些非核心但至关重要的功能（如安全的代码执行和AI辅助分析）委托给专业的第三方服务。这种设计符合现代微服务架构思想，既保证了安全性，又提高了开发效率。

这种分层解耦的架构确保了系统的每个部分都可以独立开发、测试和部署，极大地提高了项目的可维护性和可扩展性。

5.2 Database Design (数据库设计)
数据库是本系统的基石，它负责持久化ITS三模型所需的所有数据。基于需求分析，本项目采用关系型数据库来管理数据，并设计了如图5.2所示的实体关系图（ERD）。



Figure 5.2: Entity-Relationship Diagram for the Learning Platform (学习平台实体关系图)

5.2.1 Schema Description (数据表结构描述)
ER图中的核心数据实体直接映射并支撑了ITS的三个理论模型，其设计如下：

内容模型 (Content Model) 的实现:

Modules (模块): 作为课程内容的顶层组织单元，该实体通过唯一标识符、标题和描述等核心属性，对课程进行高层次的分类与归纳。

Lessons (课程): 作为知识传授的核心单元，该实体除基本信息外，包含一个自引用的外键，用于定义课程之间的前置依赖关系，从而构建出结构化的学习路径。

Exercises (练习): 与Lessons实体一对多关联，用于提供编程实践任务。

Test_Cases (测试用例): 与Exercises实体一对多关联，包含预期输出和是否对用户隐藏的布尔标志，共同构成了练习的自动化评判标准。

学习者模型 (Learner Model) 的实现:

Users (用户): 存储平台用户的基本信息，是所有学习行为数据的主体。

User_Progress (用户进度): 该实体是学习者模型中“覆盖模型 (Overlay Model)”的核心实现。它通过外键将用户与课程关联，记录了学习状态（如“进行中”、“已完成”）、尝试次数及平均解题用时等关键指标。

交互模型 (Interaction Model) 的实现:

Submissions (提交记录): 记录用户的每一次代码提交尝试。该实体存储了代码文本、评判结果、错误类型以及代码质量分析指标，为交互过程提供了完整的历史快照。

Feedback_Instances (反馈实例): 与Submissions实体一对多关联，记录了系统为单次提交生成的所有类型的反馈（如技术性、AI模型A、AI模型B），确保了反馈数据的完整性和可追溯性。

Feedback_Effectiveness (反馈效果): 该实体的设计是实现自适应教学闭环的关键。它关联了特定的反馈实例与用户的后续行为，通过记录后续尝试次数和最终成功与否，为量化评估不同反馈策略的有效性提供了数据基础，从而为未来系统的智能化迭代提供了可能。

该数据库设计不仅完整地支撑了ITS三大模型的功能需求，其前瞻性的设计也为未来的数据分析和平台改进预留了充分的扩展空间。

5.3 API Design (API设计)
系统后端遵循RESTful设计原则，通过标准的HTTP协议提供无状态的API服务，所有数据交换均采用JSON格式。根据不同的资源领域，本项目将API划分为如下几类，并设计了清晰而一致的端点：

Authentication Resource (/api/auth/): 负责处理用户认证相关的操作，包括新用户账户的创建和现有用户的身份验证。

Content Resource (/api/content/): 提供教学内容的只读访问接口，允许客户端获取课程结构和教学材料。

Submissions Resource (/api/submissions/): 处理学习交互相关的核心操作，是系统交互模型的主要入口，包括代码提交评测和提示请求等功能。

Admin Resource (/api/admin/): 提供给管理员的受保护端点，用于管理平台内容和用户数据，确保只有授权用户才能执行相应操作。

这种基于资源的RESTful API设计使系统接口结构清晰、行为一致且易于理解，为前后端的并行开发和长期维护提供了便利。

5.4 Core Interaction Design (核心交互设计)
为了直观展示系统中最复杂的业务流程之一——“学习者提交代码并获取反馈”，本项目使用UML序列图对这一过程进行了描绘（如图5.3所示）。该序列图涵盖了主要参与者及其交互，清晰呈现了代码提交从发起到反馈的完整流程。


Figure 5.3: UML Sequence Diagram for Code Submission Process (代码提交流程的UML序列图)

整个代码提交评测流程如下所示：

Learner 发起提交: 学习者在前端界面完成代码编写后，发起评测请求。

前端发送请求: 前端应用将练习标识和用户代码打包，通过POST请求发送至后端API。

后端并行处理: 后端API接收到请求后，并行地调用代码执行服务以获取运行结果，并调用AI反馈服务以生成代码分析。

服务返回结果: 代码执行服务和AI反馈服务分别返回其处理结果。

后端整合与持久化: 后端API收集并整合所有信息。随后，将此次提交的相关数据写入数据库：在Submissions表中新增一条提交记录，在Feedback_Instances表中新增反馈记录，并更新User_Progress表中的学习状态。

后端返回响应: 完成数据库操作后，后端API构造一个包含所有评判结果和反馈内容的完整JSON响应，并将其返回给前端应用。

前端更新界面: 前端应用接收并解析JSON数据，在用户界面上相应区域进行更新，向学习者展示结果。

通过该序列图可以直观地看到，系统内部各模块与外部服务是如何高效协同工作的。特别是并行处理的设计，旨在最小化用户等待时间，确保了交互的即时性。

5.5 Interface Design (界面设计)
本系统的用户界面设计遵循简洁性 (Simplicity) 和 一致性 (Consistency) 等人机交互（HCI）原则，旨在为编程初学者提供一个清爽且易于上手的学习环境。

核心学习界面: 为了最小化认知负荷 (Minimize Cognitive Load)，学习界面采用三段式布局。页面顶部展示理论内容，中部为编程题目和代码编辑器，底部则呈现结果与反馈。这种单页三段式设计，让用户可以在同一视图中完成“学习-实践-反馈”的完整闭环，无需在不同页面之间切换。

反馈展示组件: 为了在提供丰富信息的同时保证用户控制感 (User Control)，系统对多源AI反馈设计了标签页 (Tab) 式的展示组件。用户可以通过切换不同标签来自由查看不同类型的反馈信息。这种设计赋予了用户选择权，并使海量反馈信息结构化、层次化，方便初学者逐条消化吸收。

后台管理界面: 管理员界面在设计上强调一致性 (Consistency)。平台管理员通过一个统一风格的控制面板对内容和用户进行管理。该界面主要采用表格结合模态对话框 (Modal) 的方式来呈现和操作数据，降低了管理员的学习成本并减少了误操作的可能性。

5.6 Summary (本章小结)
本章从系统架构、数据库结构、API接口、核心交互流程和用户界面等多个维度，对“交互式Python智能辅导平台”进行了全面的设计。该设计方案不仅在技术上是可行的，而且在结构上是清晰和可扩展的，为第六章的系统实现奠定了坚实的基础。
Chapter 6: Implementation (系统实现)
本章详细阐述了“交互式 Python 智能辅导平台”的具体实现过程。基于第五章的系统设计蓝图，本章展示如何通过选定的技术栈，将抽象的设计模型转化为功能完备、稳定可靠的原型系统。内容涵盖开发环境的搭建、后端服务的实现、前端应用的构建以及系统的测试策略。通过展示关键代码片段和界面截图，本章旨在提供清晰、可验证的记录，证明系统已按照设计规范成功实现。
6.2 Development Environment and Tools (开发环境与工具)
本项目的开发遵循现代全栈 Web 开发的标准实践，前后端采用各自独立的技术栈和工具链，以确保开发过程的高效性和模块化。

后端环境 (Backend Environment): 后端服务基于 Python 3 环境开发。核心依赖通过 pip 进行管理，并在 requirements.txt 文件中明确列出，主要包括：

Flask 2.3.3： 作为核心 Web 框架，用于构建 RESTful API 服务。

Flask-SQLAlchemy 3.0.5： 用于实现数据持久化和 ORM 数据库操作。

Flask-JWT-Extended 4.5.3： 用于处理基于 JWT 的认证和授权。

Requests 2.31.0： 用于与外部 API（Judge0、OpenRouter 等）进行 HTTP 通信。

前端环境 (Frontend Environment): 前端单页应用（SPA）基于 Node.js 环境构建。项目的依赖管理和构建流程由 NPM 和 Vite 工具链负责：

Vite 5.3.1： 作为现代化的前端构建工具，提供极速的热模块重载（HMR）开发服务器和高效的生产构建流程。

Vue.js 3.4.29： 作为核心前端框架，用于构建响应式的用户界面。

Vue Router 4.3.3： 用于实现客户端路由和页面间导航控制。
6.3 Backend Implementation (后端实现)
后端是系统的核心引擎，负责处理业务逻辑、数据持久化以及智能分析等功能。

6.3.1 Core Application Setup (核心应用设置)
Flask 应用在 backend/app.py 中初始化。本项目采用应用工厂模式 (Application Factory Pattern) 来创建 Flask 实例。这种模式提高了应用的可测试性和可配置性。应用的核心配置（如密钥、数据库 URI、外部 API 凭证等）通过 backend/config.py 模块集中管理，并支持使用环境变量覆盖，实现开发环境与生产环境配置的隔离。

Code Snippet 6.1: Application Factory Pattern 在 app.py 中的实现

应用的 API 路由根据资源类型划分为不同的蓝图（Blueprint），例如 auth.py、content.py、submissions.py 等，并在主应用工厂中注册。这种模块化设计使 API 结构清晰且易于维护。

6.3.2 Database Implementation (数据库实现)
第五章设计的实体关系图 (ERD) 通过 SQLAlchemy ORM 转化为具体的 Python 类。在 backend/models/ 目录下，本项目将每个数据实体（如 User、Lesson、Exercise、Submission 等）定义为对应的模型类（分别位于 content_models.py 和 learning_models.py）。这些类不仅定义了表结构，还封装了相关的业务逻辑（例如，User 模型中包含密码哈希的方法）。

Code Snippet 6.2: 使用 SQLAlchemy 定义的 Submission 数据模型
通过以上方式，数据库的抽象设计（ER 图）与具体业务逻辑代码实现无缝衔接，为后续的数据操作提供了面向对象的接口。
6.3.3 API Endpoint Implementation (API 端点实现)
backend/routes/submissions.py 中的 submit_code 端点是系统交互模型的核心功能实现，它完整体现了代码提交、评估和反馈生成的业务流程。下面的代码片段展示了该端点作为指挥中心，如何调度代码执行和代码分析模块，并进行数据库更新，最后将结果返回给前端：

Code Snippet 6.3: 代码提交 API 端点的核心逻辑
如上所示，submit_code 函数作为业务流程的调度中心，顺序完成代码运行判题、AI 智能分析、数据库更新等操作，并最终构建一个完整的响应返回给前端。这个实现严格遵循第五章设计的流程，确保每一步都可靠执行。
6.3.4 External Service Integration (外部服务集成)
系统的智能化和安全性很大程度上依赖于与外部服务的集成。本项目在 backend/utils/ 目录下创建了专门的模块来封装这些交互逻辑：

代码执行 (code_executor.py): CodeExecutor 类封装了与 Judge0 在线判题 API 交互的所有细节。它负责构建提交请求、发送用户代码、轮询执行结果，并将 Judge0 返回的复杂 JSON 数据解析为系统内部统一的易用格式。该实现还内置了故障后备机制：当 Judge0 服务不可用或调用失败时，系统会捕获异常并自动回退到本地模拟执行，从而保证基本功能不中断。

AI 分析 (code_analyzer.py): CodeAnalyzer 类是 AI 教育性反馈引擎的核心实现。它通过调用 OpenRouter API，将用户代码和题目要求发送给多个 AI 模型以获取对比分析结果。下面的代码片段展示了如何通过OpenRouter API调用OpenAI的gpt-4o-mini模型。

Code Snippet 6.4: AI 代码分析引擎通过 OpenRouter API 的集成实现
6.4 Frontend Implementation (前端实现)
前端应用负责提供直观、响应式的用户界面，是用户与系统交互的直接媒介。

6.4.1 Project Structure and Routing (项目结构与路由)
前端项目采用 Vite 进行构建，主入口文件为 frontend/src/main.js。本项目使用 Vue Router 来配置单页应用的路由。在 main.js 中定义了应用的页面路由，并实现了全局路由守卫 (router.beforeEach)。路由守卫在每次导航时执行，检查用户的 JWT 身份令牌和角色信息，从而对受保护页面（如仪表板、管理员后台）的访问进行控制。例如，如果用户未登录就试图访问需要认证的页面，守卫会自动重定向到登录页；同样地，如果一个非管理员用户试图访问管理员路由，守卫将阻止并引导其到管理员登录页。

6.4.2 Core Component Implementation (核心组件实现)
Lesson.vue: 这是系统中功能最复杂、交互性最强的前端组件。它主要职责包括：

通过 API 获取并渲染课程内容和相关的编程练习列表。

集成 CodeEditor 子组件，为用户提供代码编写的编辑器界面。

管理与练习相关的各种状态数据，如当前用户代码、运行结果、反馈内容、提示信息、提交历史等。

处理用户的核心操作，例如运行代码、请求提示等，并相应调用后台 API 服务。

动态渲染多源反馈信息（技术性反馈、OpenAI 教育性反馈、DeepSeek 教育性反馈），并通过选项卡 UI 实现各反馈源之间的切换显示。

CodeEditor.vue: 该组件封装了 CodeMirror 6 代码编辑器，为上层组件提供简洁的接口。CodeEditor 通过 props 接收初始代码和配置参数（如主题模式），并通过 emits 事件将代码内容的实时变更传递给父组件。其内部实现利用 Vue 3 的组合式 API （onMounted、watch 等）在组件挂载时初始化编辑器实例，并监听属性变化来更新编辑器状态。CodeEditor 很好地将外部界面与编辑器内部细节隔离开，使得父组件（如 Lesson.vue）能够方便地与代码编辑器交互（读取或重置代码、切换主题、设为只读等）。

6.4.3 State Management and API Communication (状态管理与 API 通信)
本项目在前端实现了专门的 API 服务模块（frontend/src/api.js 和 frontend/src/adminApi.js），将所有 HTTP 请求逻辑集中管理。通过使用 Axios 库并配置全局请求拦截器，每次请求都会自动在 HTTP 请求头中附加 JWT 令牌，从而简化了前端的认证处理。此外，拦截器还添加了用户的语言偏好设置（中英），确保后台可以根据该设置返回本地化的信息。

Code Snippet 6.5: Axios 拦截器实现自动附加 JWT 认证头

组件通过调用这些 API 服务层的函数来获取数据或提交操作，并结合 Vue 3 的响应式系统（如 ref, computed）来管理状态、响应数据变化。例如，在 Lesson.vue 中，本项目使用响应式引用来跟踪当前练习的提交结果、反馈内容等，当 API 返回新数据时，响应式变量更新，界面会自动重新渲染相应部分。
6.4.4 User Interface Showcase (用户界面展示)
系统的最终实现提供了一套完整且功能丰富的用户界面。图 6.1 展示了学习者登录后看到的个人仪表板界面，图 6.2 则是核心的课程学习与练习界面，图 6.3 显示了管理员用于内容管理的后台面板。


Figure 6.1: The Learner’s Dashboard Interface (学习者仪表板界面)


Figure 6.2: The Core Learning Interface in a Lesson (核心课程学习界面)


Figure 6.3: The Administrator’s Content Management Panel (管理员内容管理面板)

6.5 Testing Strategy (测试策略)
为确保系统的质量和稳定性，本项目采用了综合性的测试策略来验证系统功能是否满足第三章定义的需求。

6.5.1 Overview of Testing Approach (测试方法概述)
本项目以手动端到端测试 (Manual End-to-End Testing) 为主要测试手段，并在开发过程中辅以组件与集成测试 (Component and Integration Testing)。这一策略确保系统不仅在各独立模块上功能正确，而且在整体业务流程中运行稳定。下面将详细介绍各部分测试的内容和结果。

6.5.2 Manual End-to-End Testing (手动端到端测试)
在最终验收阶段，本项目采用黑盒测试 (Black-box Testing) 方法，严格按照第三章 Stakeholders and Use Cases (利益相关者与用例) 中定义的用例，对系统进行了端到端的手动测试。

学习者用例测试: 本项目系统性地模拟了一名学习者的完整使用流程，包括：

用户注册和登录： 测试新用户能够成功注册账户并登录进入系统。

浏览仪表板和课程模块： 登录后验证仪表板页面正确显示用户个人进度及可用课程模块列表。

进入课程与多次提交代码： 进入某门课程后，分别测试提交正确代码（应通过所有测试用例）和包含典型错误的代码（如语法错误、逻辑错误）。观察系统对于正确和错误提交的不同反馈行为。

验证反馈显示： 确认对于错误提交，系统的所有反馈类型（技术性反馈、OpenAI 教育性反馈、DeepSeek 教育性反馈）均能按照预期正确显示，且内容恰当。

提示获取功能： 在多次提交错误代码后，验证“获取提示”按钮按设计出现，并能够成功获取相应级别的提示信息。

进度更新： 当最终提交正确代码完成练习后，返回仪表板确认该课程的完成状态和进度统计数据已正确更新。

管理员用例测试: 本项目同样模拟了管理员的核心操作流程，包括：

管理员登录： 使用管理员账户成功登录并访问后台管理界面。

内容创建： 通过管理员后台界面新建一个课程模块、一门课程，以及一个包含多个测试用例的新练习。确保每一步操作后的提示信息和结果符合预期（例如新课程出现在课程列表，练习内容保存正确）。

内容编辑与删除： 测试对已有的课程内容进行编辑和删除操作。验证系统的 CRUD 功能完整无误，且前端列表相应更新。特别是删除操作后，相关的练习数据在数据库中也被正确移除。

上述手动测试覆盖了学习者和管理员两类用户的主要用例，确保系统核心功能从用户角度看是连贯且可靠的。

6.5.3 Component and Integration Testing (组件与集成测试)
在开发过程中，本项目对关键组件和集成点进行了有针对性的测试：

API 接口测试： 本项目使用 Postman 等工具对白端后端 API 端点进行了独立测试（属于白盒/灰盒方式）。通过构造各种有效和无效请求，验证每个 API 路径在不同输入下的响应格式和逻辑正确性。例如，测试 /api/submissions/submit 在代码正确或包含错误时返回的 JSON 数据结构是否符合预期。

外部服务集成测试： 这是一个关键的可靠性测试。本项目有意模拟了外部服务（Judge0 和 OpenRouter）不可用的情形：一方面，临时修改 Judge0 API 的配置（如使用错误的 API Key 或 Host）以触发执行失败；另一方面，断开网络或篡改 OpenRouter API Key 以模拟 AI 分析服务异常。测试结果表明，系统能够捕获这些异常并按设计执行后备逻辑（例如 CodeExecutor 自动回退到本地模拟执行，CodeAnalyzer 提供本地规则分析或预置的鼓励性反馈），从而验证了系统在外部依赖失效情况下的鲁棒性。

单元测试 (Unit Testing) - 概念: 由于项目时间限制，本项目未对所有模块编写完整的单元测试。然而，后端代码结构（尤其是 utils 工具模块）遵循高内-低耦合的设计理念，具有良好的可测试性。例如，CodeAnalyzer 中的关键函数可以独立调用，以验证特定输入下分析结果的正确性。这为未来扩展单元测试套件奠定了基础。

6.5.4 Test Report Summary (测试报告总结)
通过上述测试流程，本项目确认第三章定义的所有核心功能需求都得到了满足和验证。测试过程中发现的若干次要界面瑕疵和非关键性 bug 已及时修复。最终交付的原型系统运行稳定，未发现影响核心功能的严重问题，证明本项目的实现既符合预期功能，又具备良好的用户体验和可靠性。

6.6 Summary (本章小结)
本章详细记录了将第五章设计蓝图转化为具体软件产品的实现全过程。通过结合 Flask 后端、Vue.js 前端以及多个外部服务，本项目成功构建了一个功能完备的全栈应用。本章展示了关键的后端业务逻辑实现、前端交互设计，以及与外部服务集成的细节。借助严格的模块化设计和贯穿开发始终的测试策略，本项目确保原型系统不仅满足所有功能需求，而且具备良好的稳定性和可维护性，为接下来的评估和部署奠定了坚实基础。



[1]	R. P. Medeiros, G. L. Ramalho, and T. P. Falcão, “A Systematic Literature Review on Teaching and Learning Introductory Programming in Higher Education,” IEEE Transactions on Education, vol. 62, no. 2, pp. 77-90, 2019.
[2]	D.-M. Córdova-Esparza, J.-A. Romero-González, K.-E. Córdova-Esparza, J. Terven, and R.-E. López-Martínez, “Active Learning Strategies in Computer Science Education: A Systematic Review,” Multimodal Technologies and Interaction, vol. 8, no. 6, 2024.
[3]	M. Alghamdi, “Improving Programming Skills: The Impact of Interactive E-Learning Tools on Student Success,” CLEI electronic journal, vol. 28, no. 1, 2025.
[4]	S. Marwan, G. Gao, S. Fisk, T. W. Price, and T. Barnes, “Adaptive Immediate Feedback Can Improve Novice Programming Engagement and Intention to Persist in Computer Science,” in Proceedings of the 2020 ACM Conference on International Computing Education Research, Virtual Event, New Zealand, 2020, pp. 194–203.
[5]	X. Wei, S. Sun, D. Wu, and L. Zhou, “Personalized Online Learning Resource Recommendation Based on Artificial Intelligence and Educational Psychology,” Front Psychol, vol. 12, pp. 767837-767837, 2021.
[6]	O. A. Alasmari, J. Singer, and M. B. Ada, “Do Current Online Coding Tutorial Systems Address Novice Programmer Difficulties?,” in Proceedings of the 15th International Conference on Education Technology and Computers, Barcelona, Spain, 2024, pp. 242–248.
[7]	L. S. Vygotskiĭ, "Mind in society : the development of higher psychological processes / (by) L.S. Vygotsky
edited by Michael Cole ... (et al.) (translated from the Russian)," M. Cole, ed., Cambridge, Mass.
London : Harvard University Press, 1978.
[8]	D. Wood, J. S. Bruner, and G. Ross, “THE ROLE OF TUTORING IN PROBLEM SOLVING,” J Child Psychol Psychiatry, vol. 17, no. 2, pp. 89-100, 1976.
[9]	J. Hattie, and H. Timperley, “The Power of Feedback,” Review of educational research, vol. 77, no. 1, pp. 81-112, 2007.
[10]	V. J. Shute, “Focus on Formative Feedback,” Review of educational research, vol. 78, no. 1, pp. 153-189, 2008.
[11]	D. Sleeman, and J. S. Brown, Intelligent tutoring systems: London: Academic Press, 1982.
[12]	K. VanLehn, “The behavior of tutoring systems,” International journal of artificial intelligence in education, vol. 16, no. 3, pp. 227-265, 2006.
[13]	E. Wenger, “Artificial intelligence and tutoring systems: computational and cognitive approaches to the communication of knowledge: E Wenger, Morgan Kaufman Inc (1987) 486pp£ 29.95,” Knowledge-Based Systems, vol. 1, no. 5, pp. 311-311, 1988.
[14]	I. Padayachee, "Intelligent tutoring systems: Architecture and characteristics." pp. 1-8.
[15]	B. Carr, and I. P. Goldstein, “Overlays: A theory of modelling for computer aided instruction,” 1977.
[16]	R. K. Sawyer, The Cambridge handbook of the learning sciences: Cambridge University Press, 2005.
[17]	B. P. Woolf, Building intelligent interactive tutors: Student-centered strategies for revolutionizing e-learning: Morgan Kaufmann, 2010.
[18]	R. Shen, and M. J. Lee, "Learners’ perspectives on learning programming from interactive computer tutors in a mooc." pp. 1-5.
[19]	J. H. Sharp, “Using Codecademy Interactive Lessons as an Instructional Supplement in a Python Programming Course,” Information Systems Education Journal, vol. 17, no. 3, pp. 20-28, 2019.
[20]	L. Zhang, and Y. Ma, “A study of the impact of project-based learning on student learning effects: a meta-analysis study,” Front Psychol, vol. 14, pp. 1202728-1202728, 2023.
[21]	O. Kurniawan, C. M. Poskitt, H. Ismam Al, N. T. S. Lee, C. Jégourel, and N. Sockalingam, “How Helpful do Novice Programmers Find the Feedback of an Automated Repair Tool?,” arXiv.org, 2023.
[22]	A. Anwar, I. U. Haq, I. A. Mian, F. Shah, R. Alroobaea, S. Hussain, S. S. Ullah, and F. Umar, “Applying Real-Time Dynamic Scaffolding Techniques during Tutoring Sessions Using Intelligent Tutoring Systems,” Mobile information systems, vol. 2022, pp. 1-9, 2022.
[23]	K. Rivers, and K. R. Koedinger, “Data-Driven Hint Generation in Vast Solution Spaces: a Self-Improving Python Programming Tutor,” International journal of artificial intelligence in education, vol. 27, no. 1, pp. 37-64, 2017.
[24]	X. Hou, B. J. Ericson, and X. Wang, “Parsons Problems to Scaffold Code Writing: Impact on Performance and Problem-Solving Efficiency,” in Proceedings of the 2023 Conference on Innovation and Technology in Computer Science Education V. 2, Turku, Finland, 2023, pp. 665.
[25]	H. Keuning, J. Jeuring, and B. Heeren, “A Systematic Literature Review of Automated Feedback Generation for Programming Exercises,” ACM transactions on computing education, vol. 19, no. 1, pp. 1-43, 2019.





Project Progress Summary

Abstract:
This project presents the analysis, design, and implementation of an interactive Python learning website tailored for programming beginners, especially students from non-Computer Science backgrounds. By providing an online coding environment with an integrated instant feedback mechanism, this system aims to address core barriers faced by novices, such as complex environment setup and a lack of immediate support. This proposal details the project's motivation, theoretical foundation, system requirements, and the planned evaluation methodology.

Project Objectives:
To design and develop a web-based Python learning platform that requires no local environment configuration.
To implement an automated code evaluation system capable of providing instant and targeted feedback.
To build personalised learning pathways based on established educational technology models.
To evaluate and improve the platform's usability and educational effectiveness through user testing.

1.Introduction
1.1 Context and Rationale
In the current technology-driven era, programming is evolving from a specialist skill into a general core competency essential for enhancing personal and professional capabilities. However, for beginners without a technical background, the entry barrier to programming remains significant. A primary hurdle is the complexity of setting up a local development environment, which can be an intimidating and frustrating experience for novices.
Furthermore, effective learning depends on timely feedback. Research in the field of computer science education highlights that providing immediate and constructive feedback is crucial for maintaining learner motivation and persistence. Traditional learning resources like textbooks and video tutorials have limitations in offering this level of interactive support.
This project, therefore, aims to fill this gap. By developing a highly interactive and responsive online learning platform, it seeks to lower the initial barrier to learning programming. The platform will feature a browser-based code editor, removing the need for environment setup, and will provide real-time code execution and feedback. We believe such a system can significantly improve the learning efficiency and success rate of beginners by creating a more engaging and less frustrating educational experience.
2.Application Domain
2.1Background: E-learning Technologies
This project is grounded in the fields of E-learning and Learning Technologies. To effectively address the common challenges faced by programming novices, modern Intelligent Tutoring Systems often rely on three core theoretical models: the Learner Model, the Content Model, and the Interaction Model. Together, they form the theoretical foundation for an adaptive and personalised learning environment.

Learner Model: This model is a dynamic profile of the individual learner, responsible for storing and updating their personal information, knowledge state, learning history, and common error patterns. An effective Learner Model is the prerequisite for personalisation, enabling the system to recommend suitable learning paths and exercises.

Content Model: This model is a structured representation of the knowledge domain being taught. It defines how learning materials are organised and how knowledge topics relate to one another, particularly their dependencies. For example, the Content Model ensures that a user must understand 'variables' before they can proceed to learn 'loops'.

Interaction Model: This model defines the ways in which the learner engages with the system. It embodies educational principles such as 'learning by doing' and 'instant feedback'. Through carefully designed interaction mechanisms, such as interactive coding exercises and context-aware hints, the system can guide the learner to actively explore concepts and correct mistakes promptly.

In summary, these three models work in a closed loop: the system understands the student via the Learner Model, provides appropriate materials from the Content Model, and facilitates the learning process through the Interaction Model. The outcomes of these interactions are then used to update the Learner Model, thus creating a truly personalised learning journey for each user.
2.2Workflow Example
To illustrate how these models work together, consider the following scenario: A user named 'Alex' (whose Learner Model profile indicates they have mastered 'variables') intends to learn about 'loops'.
1.Interaction Trigger: Alex clicks on the 'Loops' chapter in the course list.
2.Prerequisite Check: The system, guided by the Interaction Model, queries the Content Model and identifies that 'variables' is a prerequisite for 'loops'.
3.Learner Validation: The system accesses Alex's Learner Model and confirms that this prerequisite has been met.
4.Content Delivery: The system retrieves the 'loops' tutorial and exercises from the Content Model and presents them to Alex via the user interface (the Interaction Model).
5.User Practice & Feedback: 
1.Alex writes code in the browser-based editor and clicks 'Run'. The Interaction Model captures this event and sends the code to the backend.
2.The code is evaluated, and it fails to pass a predefined test case.
3.The Interaction Model immediately displays a feedback message, such as "Incorrect output. Check your loop's termination condition."
6.Success & State Update: 
1.Alex revises the code based on the feedback and runs it again, this time passing all tests.
2.The Interaction Model displays a congratulatory message.
3.The system then updates Alex's Learner Model, marking the 'loops' topic as 'completed'.



2.3Evaluation of Existing Systems

Platforms such as freeCodeCamp will be reviewed using the above three-model framework. While they offer well-structured content, their feedback is often too technical for true beginners.

2.4Desirable Features in a Generic System

A strong learner model to track progress and common errors
A well-structured content model that respects topic dependencies
An interaction model with highly targeted, supportive feedback that encourages problem-solving without giving direct answers
3.Requirements Analysis
3.1Requirements Overview
The goal of this project is to build an interactive web-based platform to help Python programming beginners—especially non-CS students—learn basic programming skills effectively. The system must be easy to use, provide timely feedback, and support a progressive learning path.
3.2Evaluation of Current Systems
Existing platforms excel in content structure but lack personalization and beginner-friendly guidance. Many offer technical error messages that confuse novices. This project will analyze those limitations through the three-model framework to guide the new system's design.
3.3User Requirements
UR1: As a new user, I want to easily register for an account and log in, so that my learning progress can be saved.
UR2: As a learner, I want to see a clear list of courses and my completion status for each.
UR3: As a learner, I want to read the lesson material and practice coding on the same page to avoid switching between windows.
UR4: As a learner, I want to run my code directly in the browser without any local setup and see the output or error messages immediately.
UR5: As a learner who is stuck on a problem, I want to receive a useful hint to help me find a solution.
3.4System Requirements
Functional Requirements (FR):
FR1: The system must provide user authentication features, including registration, login, and session management.
FR2: The system must store each user's learning progress, including a list of completed lessons.
FR3: The system must provide a browser-based code editor with Python syntax highlighting.
FR4: The system must provide simple and easy-to-understand lesson content in a structured format.
FR5: The system must be able to automatically evaluate user-submitted code against predefined test cases and return the result.
Non-functional Requirements (NFR):
Performance: The system's response time, from receiving a code submission to returning a result, should not exceed 3 seconds for simple exercises.
Security: User passwords must be hashed with a salt before being stored in the database.
Reliability: The system must handle common user code errors (e.g., infinite loops) gracefully without crashing the service.
Usability: The user interface must be clean and intuitive, allowing first-time users to understand its core functions without a tutorial.
8. Critical Evaluation (Preliminary Plan)
8.1Evaluation of Process
The project will adopt an incremental development model, starting with a Minimum Viable Product (MVP). The proposed technology stack is Python with the Flask framework for the backend, SQLite for the database (for ease of development), and Vue.js for a dynamic and responsive frontend. A Gantt chart will be used for project scheduling and progress tracking.

8.2Evaluation of Product
Conformance to specification (Vertical): The final product will be evaluated against all requirements listed in Section 3 to verify that it meets the predefined specifications.
Comparison with other systems (Horizontal): A comparative analysis will be conducted against 1-2 mainstream competitors (e.g., freeCodeCamp) on aspects such as user experience, feedback quality, and learning path design.
Client/user evaluation: A user study is planned, involving 4 students from the University of Birmingham with no prior programming experience. Participants will be asked to complete the first three course units. Data will be collected via questionnaires and semi-structured interviews to evaluate the system's usability and effectiveness.
Pedagogical Foundation   (Intelligent Tutoring System, ITS)
1.Scaffolding
The first idea is Scaffolding, which comes from the theory of ZPD (Zone of Proximal Development).
ZPD is the gap between what a student can do alone, and what they can do with help.
Scaffolding means giving the right help to cross this gap. For example, giving hints.
The system gives help when the student needs it, and removes the help when the student gets better.
2. Instant Feedback
The second idea is Instant Feedback.
Studies show that beginners need feedback fast to fix mistakes and stay motivated.
My system gives feedback right away. This is a big advantage.
My three models (Learner, Content, Interaction) use these two main ideas.

Learner Model
1. Overview
Definition  
The learner model is a live profile that the system keeps for every user. It stores what the learner knows, how the learner studies, and past results.

Main goals
Personalised teaching:give real-time data to the content model and the interaction model, so the system can show the right lesson and the right feedback.
Measured progress:turn actions into numbers (finished / tries / average time) so we can analyse learning and judge the system.

For the Learner Model, I use the Overlay Model.
The idea is simple: The system has a map of all topics. The Overlay Model tracks which topics the student has "completed" on that map.
In my system: The User_Progress table is the Overlay Model. It stores the status (completed, in_progress) for each user and each lesson.

2.Design principles
Principle	Meaning
Focus on core	First build progress tracking, status control, and performance record.
Keep it small	Use simple tables and rule-based updates; finishable in a three-month project.
Data-driven	Only user actions change the model; all advice comes from stored data.






3.Database schema
Table	Key columns	Purpose
Users	user_id PK, username, password_hash, created_at	Basic user info.
User_Progress	progress_id PK, user_id FK, lesson_id FK, status (not_started / in_progress / completed), attempts, avg_time_sec	Track state for each lesson.
Submissions	submission_id PK, user_id FK, exercise_id FK, is_correct, error_type, exec_time_sec, timestamp	Full history of code runs.

Personalisation signals
Attempts:how many tries the user made on this lesson.
avg_time_sec:average solving time; shows how “stuck” the learner is.
Example: if the average time is 1.5 × site average or tries > 3, the UI shows a small hint button.

4.Update flow (rule summary)
Situation	Trigger	Main database actions
First visit	open lesson or first submit	insert row in User_Progress, set status='in_progress', attempts=0, avg_time_sec=NULL
Wrong answer	is_correct = False	insert row in Submissions; attempts += 1; recalculate avg_time_sec
Correct answer	is_correct = True	insert row in Submissions; set status='completed'; update attempts, avg_time_sec


5.Links to other models
Content model uses to decide if the next lesson is unlocked.status
Interaction model uses and to decide when to show hints or examples.attempts avg_time_sec

6.Example run
User 101 studies Lesson 2.
First open → status = in_progress attempts = 0
First wrong submit (15.5 s) → attempts = 1 avg_time_sec = 15.5
Second submit correct (20.2 s) → status = completed attempts = 2 avg_time_sec = 17.85

7. Summary
With only three tables the model captures progress and two easy signals for personal help. This is enough for the MVP. More fine-grained error analysis or a probability mastery model(Bayesian Knowledge Tracking) can be added later.


Content Model
1.Overview
Definition :The content model is a structured map that shows what we teach in an entry-level Python course and in which order.

Main goals
Structured content :break the topic into Modules → Lessons(KCs) → Exercises.
Guided path :each lesson can point to a prerequisite lesson. A learner must finish the prerequisite before opening the next lesson.
Easy auto-grading :every exercise stores standard test cases, so the system can check the code at once.

For the Content Model, I use Knowledge Components, or KCs.
A KC is one small piece of knowledge. For example, a "variable" is one KC, and a "for loop" is another KC.
In my system: Each row in the Lessons table is one KC.
The model also defines the order to learn the KCs (the prerequisites).

2.Design principles
Principle	Short note
Structured & modular	clear hierarchy: Module → Lesson(KCs) → Exercise → Test Case
Dependency navigation	field prerequisite_lesson_id locks or unlocks a lesson
Practice first	every lesson has at least one coding task; pass the task to unlock the next lesson

3.Database schema
Table	Key columns	Purpose
Modules	module_id PK, title, description, order_index	top-level group (e.g. “Control Flow”)
Lessons	lesson_id PK, module_id FK, title, content_md, prerequisite_lesson_id, order_index	one theory page plus tasks
Exercises	exercise_id PK, lesson_id FK, problem_statement, hints (JSON)	coding task and optional hints
Test_Cases	test_case_id PK, exercise_id FK, input_data, expected_output	unit tests for auto-grading

4.Links to other models
Unlock rule (called by the interaction model):
User asks for Lesson B.
System reads .prerequisite_lesson_id
If not completed in , the system says “Please finish Lesson A first”.User_Progress
Otherwise it sends Lesson B, the exercise and the test cases.

5.Summary
With four small tables and one prerequisite field, the content is structured, navigable and auto-gradable. Together with the learner model and the interaction model it gives each learner the right topic at the right time and instant feedback on code.

Interaction Model
1.Overview
Definition:The interaction model is a rule-based layer that tells the system how to react to every user action. It answers the question “How should the system teach?”

Main goals
Data-driven decisions :use data from the learner model (status, tries, time) and the content model (prerequisites) to choose each next step.
Scaffolding support :give graded hints when a learner is in trouble; never show the full answer at once.
Automatic learning loop :keep repeating: show content → let user code → auto-check → give feedback / unlock next lesson.

2.Design principles
Principle	Meaning
Rule-driven	Clear “if-then” rules, no heavy AI; easy to build and explain.
Constructive feedback	Feedback is specific and helpful; success is praised quickly.
Minimal intervention	Let users explore; step in only after clear trouble signs (many errors or long delay).

3.Core rules 
Scenario	Trigger	System action
Open a lesson	User asks for Lesson B; Lesson B has prerequisite Lesson A	If User_Progress(A).status ≠ completed → show message “Please finish Lesson A first.” Otherwise send Lesson B page and its exercise.
Submit code – correct	is_correct = True	Set status = completed; send positive message “Great, lesson completed!”
Submit code – wrong output	is_correct = False (no exception)	Add failure record; attempts += 1; update avg_time_sec; show diff: “Expected …, your result …”
Submit code – runtime error	Code raises SyntaxError, NameError, …	Add failure record; update counts; show hint matched to error_type, e.g. “SyntaxError: missing colon on line 3?”
Hint by tries	status = in_progress and attempts ≥ 3	Show a non-intrusive button “Need a hint?”
Hint by time	status = in_progress and avg_time_sec > 90 s	Auto pop-up: “It seems you are stuck. Need help?”
Give graded hint	User clicks “Need a hint?”	Return next item from hints[] list (first click → first hint, second → second hint, …)

4.How the three models work together

sequenceDiagram
  participant U as Learner
  participant I as Interaction Layer
  participant C as Content Model
  participant L as Learner Model

  %% ---------- lesson request ----------
  U->>I: request Lesson B
  I->>C: query B.prerequisite
  C-->>I: prerequisite = Lesson A
  I->>L: read Progress(A).status
  alt not completed
    I-->>U: "Please complete Lesson A first."
  else completed
    I-->>U: deliver Lesson B page + exercise
  end

  %% ---------- code submission ----------
  U->>I: submit code
  I->>Server: POST /api/submit
  Server-->>I: is_correct, error_type

  alt correct
    I->>L: set status = completed
    I-->>U: "Great, lesson finished."
  else incorrect
    I->>L: attempts ++, update avg_time
    I-->>U: detailed feedback
    alt hint threshold reached
      I-->>U: show scaffold hint
    end
  end

5. Summary
With a small set of transparent rules, this model links what to learn (content) and who is learning (learner data). The rules map directly to backend APIs and front-end messages, so the system is simple, explainable, and efficient for personalisation.

Teaching Material Design

1.Pedagogical Approach
The success of an educational system depends equally on its technical implementation and the quality of its teaching content. The design of our teaching materials is guided by the following principles:

Microlearning: We break down complex programming topics into small, "bite-sized" lessons. Each lesson focuses on one core concept and is designed to be completed in 10-15 minutes. This approach helps to reduce the cognitive load on beginners.

Practice-Oriented: We believe in "Learning by Doing" as the most effective way to learn programming. Every theoretical explanation is immediately followed by a hands-on coding exercise to reinforce the new knowledge.

Scaffolded Support: When a learner gets stuck, the system provides graded hints. These hints guide the learner step-by-step, starting with a general direction and leading to more specific advice, without giving away the final answer directly.

2.Curriculum Outline (with Prerequisites)
Module	Lesson (ID & Title)	Prerequisite Lesson ID
M1 First Steps	1.1 What is Programming?	–
	1.2 First program print("Hello, World!")	1.1
	1.3 Comments in code	1.2
	1.4 Variables	1.3
	1.5 Data types: numbers & text	1.4
M2 Working with Data	2.1 Strings	1.5
	2.2 Numbers & maths	1.5
	2.3 User input input()	2.1 or 2.2
M3 Logic & Control	3.1 Booleans & comparisons	1.5
	3.2 Conditionals if elif else	3.1
	3.3 Lists	1.5
	3.4 for loop	3.3
	3.5 while loop	3.2
M4 Structuring Code	4.1 Functions	3.2
	4.2 Function parameters & returns	4.1

3.Exercise and Feedback Design

Each exercise is designed based on the following principles to maximize learning effectiveness:
Clear Instructions: The goal of each task is described in simple, plain language, avoiding technical jargon wherever possible.
Single-Objective: Each exercise is designed to test one core concept at a time. This prevents confusion by not mixing multiple new ideas.
Comprehensive Test Cases: Every exercise is supported by multiple test cases to ensure the user's code is robust. This includes:
Standard Cases: To check if the basic functionality is correct.
Edge Cases: To check special or tricky situations (e.g., using zero, an empty list, or negative numbers).

High-Quality, Graded Hints: Each exercise has 2-3 pre-written hints. For an exercise that asks to sum all numbers in a list, the hints would be:
Hint 1: "You might need a loop to go through each number in the list."
Hint 2: "Don't forget to create a variable (like total_sum = 0) before the loop to store the sum."
Hint 3: "Take a look at the for element in my_list: syntax. It might be helpful."

Technical Implementation Plan
1.Overview
This plan explains how the three conceptual models (Learner, Content, Interaction) will become a working web site. The goal is to use modern but lightweight tools that a student team can finish within one semester.

2.System Architecture
Layer	Role	Main tools
Frontend (SPA)	Shows the pages, code editor, and feedback	Vue 3, Fetch API, CodeMirror
Backend API	Handles login, progress, content lookup, hint logic	Flask 2 (Python 3)
Code-Execution Service	Runs learner code safely in a container	External API (Judge0 or Piston)

3.Technology Stack
Component	Choice	Why this choice works for beginners
Backend	Python + Flask	Same language as the course, tiny learning curve, huge community.
Frontend	Vue.js	Easiest modern framework; reactive UI for live results.
Database	SQLite (dev) → PostgreSQL (deploy)	Start with zero config; switch to a stronger DB when needed.
Sandbox	Judge0 / Piston API	Removes the risk of building your own secure container.



Pedagogical Foundation   (Intelligent Tutoring System, ITS)
1.Scaffolding
The first idea is Scaffolding, which comes from the theory of ZPD (Zone of Proximal Development).
ZPD is the gap between what a student can do alone, and what they can do with help.
Scaffolding means giving the right help to cross this gap. For example, giving hints.
The system gives help when the student needs it, and removes the help when the student gets better.
2. Instant Feedback
The second idea is Instant Feedback.
Studies show that beginners need feedback fast to fix mistakes and stay motivated.
My system gives feedback right away. This is a big advantage.
My three models (Learner, Content, Interaction) use these two main ideas.

Learner Model
1. Overview
Definition  
The learner model is a live profile that the system keeps for every user. It stores what the learner knows, how the learner studies, and past results.

Main goals
Personalised teaching:give real-time data to the content model and the interaction model, so the system can show the right lesson and the right feedback.
Measured progress:turn actions into numbers (finished / tries / average time) so we can analyse learning and judge the system.

For the Learner Model, I use the Overlay Model.
The idea is simple: The system has a map of all topics. The Overlay Model tracks which topics the student has "completed" on that map.
In my system: The User_Progress table is the Overlay Model. It stores the status (completed, in_progress) for each user and each lesson.

2.Design principles
Principle	Meaning
Focus on core	First build progress tracking, status control, and performance record.
Keep it small	Use simple tables and rule-based updates; finishable in a three-month project.
Data-driven	Only user actions change the model; all advice comes from stored data.






3.Database schema
Table	Key columns	Purpose
Users	user_id PK, username, password_hash, created_at	Basic user info.
User_Progress	progress_id PK, user_id FK, lesson_id FK, status (not_started / in_progress / completed), attempts, avg_time_sec	Track state for each lesson.
Submissions	submission_id PK, user_id FK, exercise_id FK, is_correct, error_type, exec_time_sec, timestamp	Full history of code runs.

Personalisation signals
Attempts:how many tries the user made on this lesson.
avg_time_sec:average solving time; shows how “stuck” the learner is.
Example: if the average time is 1.5 × site average or tries > 3, the UI shows a small hint button.

4.Update flow (rule summary)
Situation	Trigger	Main database actions
First visit	open lesson or first submit	insert row in User_Progress, set status='in_progress', attempts=0, avg_time_sec=NULL
Wrong answer	is_correct = False	insert row in Submissions; attempts += 1; recalculate avg_time_sec
Correct answer	is_correct = True	insert row in Submissions; set status='completed'; update attempts, avg_time_sec


5.Links to other models
Content model uses to decide if the next lesson is unlocked.status
Interaction model uses and to decide when to show hints or examples.attempts avg_time_sec

6.Example run
User 101 studies Lesson 2.
First open → status = in_progress attempts = 0
First wrong submit (15.5 s) → attempts = 1 avg_time_sec = 15.5
Second submit correct (20.2 s) → status = completed attempts = 2 avg_time_sec = 17.85

7. Summary
With only three tables the model captures progress and two easy signals for personal help. This is enough for the MVP. More fine-grained error analysis or a probability mastery model(Bayesian Knowledge Tracking) can be added later.


Content Model
1.Overview
Definition :The content model is a structured map that shows what we teach in an entry-level Python course and in which order.

Main goals
Structured content :break the topic into Modules → Lessons(KCs) → Exercises.
Guided path :each lesson can point to a prerequisite lesson. A learner must finish the prerequisite before opening the next lesson.
Easy auto-grading :every exercise stores standard test cases, so the system can check the code at once.

For the Content Model, I use Knowledge Components, or KCs.
A KC is one small piece of knowledge. For example, a "variable" is one KC, and a "for loop" is another KC.
In my system: Each row in the Lessons table is one KC.
The model also defines the order to learn the KCs (the prerequisites).

2.Design principles
Principle	Short note
Structured & modular	clear hierarchy: Module → Lesson(KCs) → Exercise → Test Case
Dependency navigation	field prerequisite_lesson_id locks or unlocks a lesson
Practice first	every lesson has at least one coding task; pass the task to unlock the next lesson

3.Database schema
Table	Key columns	Purpose
Modules	module_id PK, title, description, order_index	top-level group (e.g. “Control Flow”)
Lessons	lesson_id PK, module_id FK, title, content_md, prerequisite_lesson_id, order_index	one theory page plus tasks
Exercises	exercise_id PK, lesson_id FK, problem_statement, hints (JSON)	coding task and optional hints
Test_Cases	test_case_id PK, exercise_id FK, input_data, expected_output	unit tests for auto-grading

4.Links to other models
Unlock rule (called by the interaction model):
User asks for Lesson B.
System reads .prerequisite_lesson_id
If not completed in , the system says “Please finish Lesson A first”.User_Progress
Otherwise it sends Lesson B, the exercise and the test cases.

5.Summary
With four small tables and one prerequisite field, the content is structured, navigable and auto-gradable. Together with the learner model and the interaction model it gives each learner the right topic at the right time and instant feedback on code.

Interaction Model
1.Overview
Definition:The interaction model is a rule-based layer that tells the system how to react to every user action. It answers the question “How should the system teach?”

Main goals
Data-driven decisions :use data from the learner model (status, tries, time) and the content model (prerequisites) to choose each next step.
Scaffolding support :give graded hints when a learner is in trouble; never show the full answer at once.
Automatic learning loop :keep repeating: show content → let user code → auto-check → give feedback / unlock next lesson.

2.Design principles
Principle	Meaning
Rule-driven	Clear “if-then” rules, no heavy AI; easy to build and explain.
Constructive feedback	Feedback is specific and helpful; success is praised quickly.
Minimal intervention	Let users explore; step in only after clear trouble signs (many errors or long delay).

3.Core rules 
Scenario	Trigger	System action
Open a lesson	User asks for Lesson B; Lesson B has prerequisite Lesson A	If User_Progress(A).status ≠ completed → show message “Please finish Lesson A first.” Otherwise send Lesson B page and its exercise.
Submit code – correct	is_correct = True	Set status = completed; send positive message “Great, lesson completed!”
Submit code – wrong output	is_correct = False (no exception)	Add failure record; attempts += 1; update avg_time_sec; show diff: “Expected …, your result …”
Submit code – runtime error	Code raises SyntaxError, NameError, …	Add failure record; update counts; show hint matched to error_type, e.g. “SyntaxError: missing colon on line 3?”
Hint by tries	status = in_progress and attempts ≥ 3	Show a non-intrusive button “Need a hint?”
Hint by time	status = in_progress and avg_time_sec > 90 s	Auto pop-up: “It seems you are stuck. Need help?”
Give graded hint	User clicks “Need a hint?”	Return next item from hints[] list (first click → first hint, second → second hint, …)

4.How the three models work together

sequenceDiagram
  participant U as Learner
  participant I as Interaction Layer
  participant C as Content Model
  participant L as Learner Model

  %% ---------- lesson request ----------
  U->>I: request Lesson B
  I->>C: query B.prerequisite
  C-->>I: prerequisite = Lesson A
  I->>L: read Progress(A).status
  alt not completed
    I-->>U: "Please complete Lesson A first."
  else completed
    I-->>U: deliver Lesson B page + exercise
  end

  %% ---------- code submission ----------
  U->>I: submit code
  I->>Server: POST /api/submit
  Server-->>I: is_correct, error_type

  alt correct
    I->>L: set status = completed
    I-->>U: "Great, lesson finished."
  else incorrect
    I->>L: attempts ++, update avg_time
    I-->>U: detailed feedback
    alt hint threshold reached
      I-->>U: show scaffold hint
    end
  end

5. Summary
With a small set of transparent rules, this model links what to learn (content) and who is learning (learner data). The rules map directly to backend APIs and front-end messages, so the system is simple, explainable, and efficient for personalisation.

Teaching Material Design

1.Pedagogical Approach
The success of an educational system depends equally on its technical implementation and the quality of its teaching content. The design of our teaching materials is guided by the following principles:

Microlearning: We break down complex programming topics into small, "bite-sized" lessons. Each lesson focuses on one core concept and is designed to be completed in 10-15 minutes. This approach helps to reduce the cognitive load on beginners.

Practice-Oriented: We believe in "Learning by Doing" as the most effective way to learn programming. Every theoretical explanation is immediately followed by a hands-on coding exercise to reinforce the new knowledge.

Scaffolded Support: When a learner gets stuck, the system provides graded hints. These hints guide the learner step-by-step, starting with a general direction and leading to more specific advice, without giving away the final answer directly.

2.Curriculum Outline (with Prerequisites)
Module	Lesson (ID & Title)	Prerequisite Lesson ID
M1 First Steps	1.1 What is Programming?	–
	1.2 First program print("Hello, World!")	1.1
	1.3 Comments in code	1.2
	1.4 Variables	1.3
	1.5 Data types: numbers & text	1.4
M2 Working with Data	2.1 Strings	1.5
	2.2 Numbers & maths	1.5
	2.3 User input input()	2.1 or 2.2
M3 Logic & Control	3.1 Booleans & comparisons	1.5
	3.2 Conditionals if elif else	3.1
	3.3 Lists	1.5
	3.4 for loop	3.3
	3.5 while loop	3.2
M4 Structuring Code	4.1 Functions	3.2
	4.2 Function parameters & returns	4.1

3.Exercise and Feedback Design

Each exercise is designed based on the following principles to maximize learning effectiveness:
Clear Instructions: The goal of each task is described in simple, plain language, avoiding technical jargon wherever possible.
Single-Objective: Each exercise is designed to test one core concept at a time. This prevents confusion by not mixing multiple new ideas.
Comprehensive Test Cases: Every exercise is supported by multiple test cases to ensure the user's code is robust. This includes:
Standard Cases: To check if the basic functionality is correct.
Edge Cases: To check special or tricky situations (e.g., using zero, an empty list, or negative numbers).

High-Quality, Graded Hints: Each exercise has 2-3 pre-written hints. For an exercise that asks to sum all numbers in a list, the hints would be:
Hint 1: "You might need a loop to go through each number in the list."
Hint 2: "Don't forget to create a variable (like total_sum = 0) before the loop to store the sum."
Hint 3: "Take a look at the for element in my_list: syntax. It might be helpful."

Technical Implementation Plan
1.Overview
This plan explains how the three conceptual models (Learner, Content, Interaction) will become a working web site. The goal is to use modern but lightweight tools that a student team can finish within one semester.

2.System Architecture
Layer	Role	Main tools
Frontend (SPA)	Shows the pages, code editor, and feedback	Vue 3, Fetch API, CodeMirror
Backend API	Handles login, progress, content lookup, hint logic	Flask 2 (Python 3)
Code-Execution Service	Runs learner code safely in a container	External API (Judge0 or Piston)

3.Technology Stack
Component	Choice	Why this choice works for beginners
Backend	Python + Flask	Same language as the course, tiny learning curve, huge community.
Frontend	Vue.js	Easiest modern framework; reactive UI for live results.
Database	SQLite (dev) → PostgreSQL (deploy)	Start with zero config; switch to a stronger DB when needed.
Sandbox	Judge0 / Piston API	Removes the risk of building your own secure container.
